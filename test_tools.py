"""
测试剪映MCP工具
验证所有工具是否正确注册和工作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

from mcp.server.fastmcp import FastMCP

# 导入所有工具模块
from tools.track_tools import register_track_tools
from tools.video_tools import register_video_tools
from tools.text_tools import register_text_tools
from tools.audio_tools import register_audio_tools
from tools.draft_tools import register_draft_tools
from tools.material_tools import register_material_tools

def test_tool_registration():
    """测试工具注册"""
    print("🧪 测试剪映MCP工具注册...")
    print("=" * 50)
    
    # 创建测试MCP服务器
    mcp = FastMCP("测试剪映MCP服务器")
    
    try:
        # 注册所有工具
        print("📋 注册轨道管理工具...")
        register_track_tools(mcp)
        
        print("🎬 注册视频片段工具...")
        register_video_tools(mcp)
        
        print("📝 注册字幕片段工具...")
        register_text_tools(mcp)
        
        print("🎵 注册音频片段工具...")
        register_audio_tools(mcp)
        
        print("📁 注册草稿管理工具...")
        register_draft_tools(mcp)
        
        print("🎨 注册素材工具...")
        register_material_tools(mcp)
        
        print("\n✅ 所有工具注册成功！")
        
        # 统计工具数量
        print(f"\n📊 工具统计:")
        print(f"- 总计工具数量: 预计20+个")
        print(f"- 轨道管理: 1个工具")
        print(f"- 视频片段: 7个工具")
        print(f"- 字幕片段: 2个工具")
        print(f"- 音频片段: 4个工具")
        print(f"- 草稿管理: 2个工具")
        print(f"- 素材工具: 1个工具")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具注册失败: {e}")
        return False

def show_tool_summary():
    """显示工具功能总结"""
    print("\n🎯 剪映MCP工具功能总结")
    print("=" * 50)
    
    tools_info = {
        "轨道管理": [
            "add_track - 添加轨道到草稿"
        ],
        "视频片段管理": [
            "add_video_segment - 添加视频片段",
            "add_video_transition - 添加转场特效",
            "add_video_mask - 添加视频蒙版",
            "add_video_filters - 添加视频滤镜",
            "add_video_effects - 添加视频特效",
            "add_video_background_filling - 添加背景填充",
            "add_video_animation - 添加视频动画"
        ],
        "字幕片段管理": [
            "add_text_segment - 添加字幕片段",
            "add_text_animation_effect - 添加字幕动画特效"
        ],
        "音频片段管理": [
            "add_audio_segment - 添加音频片段",
            "add_audio_keyframes - 添加音频关键帧",
            "add_audio_fade_effect - 添加淡入淡出特效",
            "add_audio_effects - 添加音频特效"
        ],
        "草稿管理": [
            "create_draft - 创建新草稿",
            "export_draft - 导出草稿为zip"
        ],
        "素材工具": [
            "get_media_info - 获取媒体文件信息"
        ]
    }
    
    for category, tools in tools_info.items():
        print(f"\n📂 {category}:")
        for tool in tools:
            print(f"   ✓ {tool}")
    
    print(f"\n🎉 总计: {sum(len(tools) for tools in tools_info.values())} 个工具")

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例")
    print("=" * 50)
    
    examples = [
        {
            "场景": "创建视频项目",
            "步骤": [
                "1. create_draft() - 创建新草稿",
                "2. add_track() - 添加视频轨道",
                "3. add_video_segment() - 添加视频片段",
                "4. add_text_segment() - 添加字幕",
                "5. export_draft() - 导出项目"
            ]
        },
        {
            "场景": "视频特效处理",
            "步骤": [
                "1. add_video_filters() - 添加滤镜美化",
                "2. add_video_effects() - 添加特效",
                "3. add_video_transition() - 添加转场",
                "4. add_video_animation() - 添加动画"
            ]
        },
        {
            "场景": "音频处理",
            "步骤": [
                "1. add_audio_segment() - 添加背景音乐",
                "2. add_audio_keyframes() - 调整音量曲线",
                "3. add_audio_fade_effect() - 添加淡入淡出",
                "4. add_audio_effects() - 添加音频特效"
            ]
        }
    ]
    
    for example in examples:
        print(f"\n🎬 {example['场景']}:")
        for step in example['步骤']:
            print(f"   {step}")

def main():
    """主函数"""
    print("🎬 剪映MCP工具测试程序")
    print("=" * 60)
    
    # 测试工具注册
    if test_tool_registration():
        show_tool_summary()
        show_usage_examples()
        
        print("\n🚀 启动说明:")
        print("- 运行 'python main.py' 启动MCP服务器")
        print("- 使用 'uv run mcp dev main.py' 进行调试")
        print("- 确保剪映API服务运行在 http://localhost:81/api/jy")
        
        print("\n✨ 测试完成！所有工具已准备就绪。")
    else:
        print("\n❌ 测试失败，请检查代码。")

if __name__ == "__main__":
    main()
