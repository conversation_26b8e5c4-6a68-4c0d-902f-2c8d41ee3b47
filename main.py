"""
简单的MCP服务器示例
演示MCP的核心功能：Tools、Resources和Prompts
"""

from mcp.server.fastmcp import FastMCP
from typing import Dict, Any
import json
from datetime import datetime

# 创建MCP服务器实例
mcp = FastMCP("JianYingDraft MCP Server")

# 模拟的剪映草稿数据
DRAFT_DATA = {
    "draft_001": {
        "title": "我的第一个视频",
        "duration": "00:02:30",
        "created_at": "2024-01-15",
        "status": "editing",
        "tracks": ["视频轨道1", "音频轨道1", "字幕轨道1"]
    },
    "draft_002": {
        "title": "旅行Vlog",
        "duration": "00:05:45",
        "created_at": "2024-01-20",
        "status": "completed",
        "tracks": ["视频轨道1", "视频轨道2", "音频轨道1"]
    }
}

# 1. Tools - 可执行的功能
@mcp.tool()
def create_draft(title: str, description: str = "") -> Dict[str, Any]:
    """创建新的剪映草稿"""
    draft_id = f"draft_{len(DRAFT_DATA) + 1:03d}"
    new_draft = {
        "title": title,
        "description": description,
        "duration": "00:00:00",
        "created_at": datetime.now().strftime("%Y-%m-%d"),
        "status": "new",
        "tracks": []
    }
    DRAFT_DATA[draft_id] = new_draft
    return {
        "success": True,
        "draft_id": draft_id,
        "message": f"成功创建草稿: {title}"
    }

@mcp.tool()
def get_draft_info(draft_id: str) -> Dict[str, Any]:
    """获取指定草稿的详细信息"""
    if draft_id not in DRAFT_DATA:
        return {"error": f"草稿 {draft_id} 不存在"}

    return {
        "draft_id": draft_id,
        "info": DRAFT_DATA[draft_id]
    }

@mcp.tool()
def list_all_drafts() -> Dict[str, Any]:
    """列出所有草稿"""
    return {
        "total_count": len(DRAFT_DATA),
        "drafts": DRAFT_DATA
    }

# 2. Resources - 数据源
@mcp.resource("jianyingdraft://drafts/{draft_id}")
def get_draft_resource(draft_id: str) -> str:
    """获取草稿资源数据"""
    if draft_id not in DRAFT_DATA:
        return f"草稿 {draft_id} 不存在"

    draft = DRAFT_DATA[draft_id]
    return json.dumps(draft, ensure_ascii=False, indent=2)

@mcp.resource("jianyingdraft://stats")
def get_stats_resource() -> str:
    """获取统计信息"""
    stats = {
        "total_drafts": len(DRAFT_DATA),
        "completed_drafts": len([d for d in DRAFT_DATA.values() if d["status"] == "completed"]),
        "editing_drafts": len([d for d in DRAFT_DATA.values() if d["status"] == "editing"]),
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    return json.dumps(stats, ensure_ascii=False, indent=2)

# 3. Prompts - 提示模板
@mcp.prompt()
def video_script_prompt(topic: str, style: str = "casual") -> str:
    """生成视频脚本的提示模板"""
    styles = {
        "casual": "轻松随意的风格",
        "professional": "专业正式的风格",
        "humorous": "幽默风趣的风格",
        "educational": "教育科普的风格"
    }

    style_desc = styles.get(style, styles["casual"])

    return f"""请为主题"{topic}"创建一个视频脚本，要求：

1. 风格：{style_desc}
2. 时长：2-3分钟
3. 结构：开场白 -> 主要内容 -> 总结/呼吁行动
4. 语言：简洁明了，适合视频表达
5. 包含适当的停顿和转场提示

请确保内容有趣且易于理解。"""

@mcp.prompt()
def edit_suggestion_prompt(draft_title: str) -> str:
    """生成编辑建议的提示模板"""
    return f"""请为视频"{draft_title}"提供编辑建议：

1. 剪辑节奏建议
2. 转场效果推荐
3. 音乐配乐建议
4. 字幕样式建议
5. 色彩调整建议

请提供具体可操作的建议。"""

def test_server_functions():
    """测试服务器功能"""
    print("🧪 测试服务器功能...")

    # 测试工具
    print("\n📋 测试 Tools:")
    result1 = create_draft("测试视频", "这是一个测试")
    print(f"创建草稿: {result1}")

    result2 = list_all_drafts()
    print(f"列出草稿: {result2}")

    result3 = get_draft_info("draft_001")
    print(f"获取草稿信息: {result3}")

    # 测试资源
    print("\n📁 测试 Resources:")
    stats = get_stats_resource()
    print(f"统计信息: {stats}")

    draft_data = get_draft_resource("draft_001")
    print(f"草稿数据: {draft_data}")

    # 测试提示
    print("\n💬 测试 Prompts:")
    script_prompt = video_script_prompt("学习编程", "educational")
    print(f"脚本提示: {script_prompt[:200]}...")

    edit_prompt = edit_suggestion_prompt("我的第一个视频")
    print(f"编辑建议: {edit_prompt[:200]}...")

    print("\n✅ 所有功能测试完成！")

def main():
    """运行MCP服务器"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试模式
        test_server_functions()
    else:
        # 正常MCP服务器模式
        print("启动 JianYingDraft MCP 服务器...")
        print("服务器提供以下功能：")
        print("- Tools: 创建草稿、获取草稿信息、列出所有草稿")
        print("- Resources: 草稿数据、统计信息")
        print("- Prompts: 视频脚本模板、编辑建议模板")
        print("\n等待客户端连接...")

        # 运行服务器
        mcp.run()

if __name__ == "__main__":
    main()
