"""
MCP (Model Context Protocol) 核心概念演示
这个脚本展示了如何创建一个简单的MCP服务器
"""

from mcp.server.fastmcp import FastMCP
from typing import Dict, Any
import json

# 创建MCP服务器 - 这是MCP的核心组件
mcp = FastMCP("MCP Demo Server")

print("🚀 MCP (Model Context Protocol) 演示")
print("=" * 50)

# 1. Tools - 工具：AI可以调用的函数
print("\n📋 1. Tools (工具) - AI可以调用的函数")
print("-" * 30)

@mcp.tool()
def calculate(operation: str, a: float, b: float) -> Dict[str, Any]:
    """执行数学计算"""
    operations = {
        "add": a + b,
        "subtract": a - b,
        "multiply": a * b,
        "divide": a / b if b != 0 else "Error: Division by zero"
    }
    
    result = operations.get(operation, "Error: Unknown operation")
    return {
        "operation": operation,
        "operands": [a, b],
        "result": result
    }

@mcp.tool()
def get_greeting(name: str, language: str = "chinese") -> str:
    """生成多语言问候语"""
    greetings = {
        "chinese": f"你好，{name}！",
        "english": f"Hello, {name}!",
        "spanish": f"¡Hola, {name}!",
        "french": f"Bonjour, {name}!"
    }
    return greetings.get(language, f"Hello, {name}!")

print("✅ 定义了工具: calculate, get_greeting")

# 2. Resources - 资源：为AI提供数据
print("\n📁 2. Resources (资源) - 为AI提供数据")
print("-" * 30)

# 模拟数据
DEMO_DATA = {
    "users": [
        {"id": 1, "name": "张三", "role": "开发者"},
        {"id": 2, "name": "李四", "role": "设计师"},
        {"id": 3, "name": "王五", "role": "产品经理"}
    ],
    "projects": [
        {"id": 1, "name": "MCP项目", "status": "进行中"},
        {"id": 2, "name": "AI助手", "status": "已完成"}
    ]
}

@mcp.resource("demo://users")
def get_users_data() -> str:
    """获取用户数据"""
    return json.dumps(DEMO_DATA["users"], ensure_ascii=False, indent=2)

@mcp.resource("demo://projects")
def get_projects_data() -> str:
    """获取项目数据"""
    return json.dumps(DEMO_DATA["projects"], ensure_ascii=False, indent=2)

@mcp.resource("demo://stats")
def get_stats_data() -> str:
    """获取统计数据"""
    stats = {
        "total_users": len(DEMO_DATA["users"]),
        "total_projects": len(DEMO_DATA["projects"]),
        "active_projects": len([p for p in DEMO_DATA["projects"] if p["status"] == "进行中"])
    }
    return json.dumps(stats, ensure_ascii=False, indent=2)

print("✅ 定义了资源: demo://users, demo://projects, demo://stats")

# 3. Prompts - 提示模板：帮助构建与LLM的交互
print("\n💬 3. Prompts (提示模板) - 帮助构建与LLM的交互")
print("-" * 30)

@mcp.prompt()
def code_review_prompt(code: str, language: str = "python") -> str:
    """代码审查提示模板"""
    return f"""请审查以下{language}代码：

```{language}
{code}
```

请从以下方面进行评估：
1. 代码质量和可读性
2. 性能优化建议
3. 安全性考虑
4. 最佳实践建议
5. 潜在的bug或问题

请提供具体的改进建议。"""

@mcp.prompt()
def project_planning_prompt(project_name: str, requirements: str) -> str:
    """项目规划提示模板"""
    return f"""项目名称：{project_name}

需求描述：
{requirements}

请帮助制定项目计划，包括：
1. 项目目标和范围
2. 主要功能模块
3. 技术栈建议
4. 开发时间线
5. 风险评估和应对策略
6. 团队角色分工建议

请提供详细的项目规划方案。"""

print("✅ 定义了提示模板: code_review_prompt, project_planning_prompt")

# MCP架构说明
print("\n🏗️ MCP 架构说明")
print("-" * 30)
print("""
MCP采用客户端-服务器架构：

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Host      │    │   MCP Client    │    │   MCP Server    │
│  (AI应用)       │◄──►│   (连接组件)    │◄──►│   (我们的代码)  │
│  Claude等       │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘

通信协议：JSON-RPC 2.0
传输方式：stdio, HTTP, SSE等
""")

# 使用示例
print("\n🎯 使用示例")
print("-" * 30)
print("""
1. AI可以调用工具：
   - calculate("add", 10, 5) → 返回计算结果
   - get_greeting("张三", "chinese") → 返回问候语

2. AI可以读取资源：
   - demo://users → 获取用户列表
   - demo://stats → 获取统计信息

3. AI可以使用提示模板：
   - code_review_prompt(code, "python") → 生成代码审查提示
   - project_planning_prompt("新项目", "需求描述") → 生成项目规划提示
""")

def demo_functions():
    """演示各个功能"""
    print("\n🧪 功能演示")
    print("-" * 30)
    
    # 演示工具
    print("工具演示:")
    calc_result = calculate("add", 10, 5)
    print(f"  calculate('add', 10, 5) = {calc_result}")
    
    greeting = get_greeting("张三", "english")
    print(f"  get_greeting('张三', 'english') = {greeting}")
    
    # 演示资源
    print("\n资源演示:")
    users = get_users_data()
    print(f"  demo://users 数据预览: {users[:100]}...")
    
    # 演示提示
    print("\n提示模板演示:")
    prompt = code_review_prompt("def hello(): print('Hello')", "python")
    print(f"  代码审查提示预览: {prompt[:150]}...")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        # 演示模式
        demo_functions()
        print("\n✨ MCP演示完成！")
        print("要启动实际的MCP服务器，请运行: python mcp_demo.py")
    else:
        # MCP服务器模式
        print("\n🚀 启动MCP服务器...")
        print("等待客户端连接...")
        mcp.run()

if __name__ == "__main__":
    main()
