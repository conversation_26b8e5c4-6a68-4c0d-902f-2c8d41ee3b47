"""
草稿管理工具
包含草稿相关的所有API工具
"""
from typing import Optional, Any, Dict

import requests

from draft.main import mcp
from tools.config import config


@mcp.tool()
def create_draft(
        width: int,
        height: int,
        fps: int,
        draft_path: str,
        name: Optional[str] = None,
        api_key: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建新的草稿

    参数:
    - width (int): 草稿宽度，必需
    - height (int): 草稿高度，必需
    - fps (int): 帧率，必需
    - draft_path (str): 草稿路径，必需
    - name (str, 可选): 草稿名称
    - api_key (str, 可选): API密钥

    返回:
    - Dict[str, Any]: API响应结果，包含草稿ID、名称和内容
    """

    # 构建请求数据
    request_data = {
        "width": width,
        "height": height,
        "fps": fps,
        "draftPath": draft_path
    }

    # 添加可选参数
    if name:
        request_data["name"] = name
    if api_key:
        request_data["apiKey"] = api_key

    # 构建请求头
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["apiKey"] ='476cfb43ba1e45a6ad3e2a8ad28b726e'

    try:
        response = requests.post(
            f"http://43.136.184.237:45124/api/jy/draft/create",
            json=request_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()

            # 检查响应格式
            if result.get("code") == 200:
                data = result.get("data", {})
                return {
                    "success": True,
                    "message": "草稿创建成功",
                    "draft_id": data.get("draftId"),
                    "draft_name": data.get("draftName"),
                    "draft_content": data.get("draftContent"),
                    "api_response": result,
                    "draft_info": {
                        "width": width,
                        "height": height,
                        "fps": fps,
                        "path": draft_path,
                        "name": name or "未命名草稿"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"草稿创建失败: {result.get('message', '未知错误')}",
                    "error_code": result.get("code"),
                    "api_response": result
                }
        else:
            return {
                "success": False,
                "message": f"草稿创建失败，HTTP状态码: {response.status_code}",
                "error": response.text,
                "status_code": response.status_code
            }

    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "message": "网络请求失败",
            "error": str(e),
            "request_data": request_data
        }
    except Exception as e:
        return {
            "success": False,
            "message": "创建草稿时发生未知错误",
            "error": str(e),
            "request_data": request_data
        }


@mcp.tool()
def export_draft(draft_id: str) -> Dict[str, Any]:
    """
    导出草稿为zip压缩包

    参数:
    - draft_id (str): 草稿ID，必需

    返回:
    - Dict[str, Any]: API响应结果，包含导出状态和文件信息
    """

    try:
        response = requests.get(
            f"{config.get_base_url()}/draft/{draft_id}/export",
            headers={"Accept": "application/octet-stream"}
        )

        if response.status_code == 200:
            # 获取文件大小
            content_length = len(response.content)

            # 尝试从响应头获取文件名
            content_disposition = response.headers.get('Content-Disposition', '')
            filename = f"draft_{draft_id}.zip"
            if 'filename=' in content_disposition:
                filename = content_disposition.split('filename=')[1].strip('"')

            return {
                "success": True,
                "message": "草稿导出成功",
                "draft_id": draft_id,
                "export_info": {
                    "filename": filename,
                    "file_size_bytes": content_length,
                    "file_size_mb": round(content_length / (1024 * 1024), 2),
                    "content_type": response.headers.get('Content-Type', 'application/zip')
                },
                "note": "文件内容已下载，可通过response.content获取二进制数据"
            }
        else:
            return {
                "success": False,
                "message": f"草稿导出失败，HTTP状态码: {response.status_code}",
                "error": response.text,
                "status_code": response.status_code,
                "draft_id": draft_id
            }

    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "message": "网络请求失败",
            "error": str(e),
            "draft_id": draft_id
        }
    except Exception as e:
        return {
            "success": False,
            "message": "导出草稿时发生未知错误",
            "error": str(e),
            "draft_id": draft_id
        }
