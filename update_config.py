"""
批量更新所有工具文件，使用统一的配置
"""

import os
import re

def update_file(file_path):
    """更新单个文件"""
    print(f"更新文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入部分
    content = re.sub(
        r'import requests\nfrom typing import Dict, Any.*?\nfrom mcp\.server\.fastmcp import FastMCP\n\n# API基础URL\nBASE_URL = ".*?"',
        'import requests\nfrom typing import Dict, Any, Optional, List\nfrom mcp.server.fastmcp import FastMCP\nfrom .config import config',
        content,
        flags=re.DOTALL
    )
    
    # 替换所有的BASE_URL使用
    content = re.sub(
        r'f"\{BASE_URL\}/',
        'f"{config.get_base_url()}/',
        content
    )
    
    # 替换请求头
    content = re.sub(
        r'headers=\{"Content-Type": "application/json"\}',
        'headers=config.get_headers(),\n                timeout=config.REQUEST_TIMEOUT',
        content
    )
    
    # 特殊处理带有apiKey的请求头
    content = re.sub(
        r'headers = \{"Content-Type": "application/json"\}\s*if api_key:\s*headers\["apiKey"\] = api_key',
        'headers = config.get_headers(api_key)',
        content,
        flags=re.DOTALL
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 更新完成")

def main():
    """主函数"""
    print("🔧 批量更新配置...")
    
    # 需要更新的文件列表
    files_to_update = [
        'tools/track_tools.py',
        'tools/video_tools.py', 
        'tools/text_tools.py',
        'tools/audio_tools.py',
        'tools/draft_tools.py',
        'tools/material_tools.py'
    ]
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            update_file(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    print("\n✨ 所有文件更新完成！")
    print("现在所有工具都使用统一的配置文件。")

if __name__ == "__main__":
    main()
