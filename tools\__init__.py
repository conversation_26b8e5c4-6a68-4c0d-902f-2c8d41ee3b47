"""
剪映MCP工具包
包含所有剪映API的MCP工具封装
"""

# 导出所有工具注册函数
from .track_tools import register_track_tools
from .video_tools import register_video_tools
from .text_tools import register_text_tools
from .audio_tools import register_audio_tools
from .draft_tools import register_draft_tools
from .material_tools import register_material_tools

__all__ = [
    'register_track_tools',
    'register_video_tools', 
    'register_text_tools',
    'register_audio_tools',
    'register_draft_tools',
    'register_material_tools'
]
