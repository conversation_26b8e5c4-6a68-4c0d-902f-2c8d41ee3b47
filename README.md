# 剪映MCP服务器

基于 Model Context Protocol (MCP) 的剪映视频编辑API工具集成服务器。

## 🎬 项目简介

这是一个完整的MCP服务器，将剪映的所有API接口封装为MCP工具，让AI能够通过标准化的方式操作剪映进行视频编辑。

## 🚀 功能特性

### 📋 完整的API覆盖
- **轨道管理**: 添加和管理视频轨道
- **视频片段**: 添加视频、转场、蒙版、滤镜、特效、背景填充、动画
- **字幕片段**: 添加字幕、动画特效
- **音频片段**: 添加音频、关键帧、淡入淡出、特效
- **草稿管理**: 创建和导出草稿
- **素材工具**: 获取媒体文件信息

### 🛠️ 工具总览 (17个工具)

#### 轨道管理 (1个)
- `add_track` - 添加轨道到草稿

#### 视频片段管理 (7个)
- `add_video_segment` - 添加视频片段
- `add_video_transition` - 添加转场特效
- `add_video_mask` - 添加视频蒙版
- `add_video_filters` - 添加视频滤镜
- `add_video_effects` - 添加视频特效
- `add_video_background_filling` - 添加背景填充
- `add_video_animation` - 添加视频动画

#### 字幕片段管理 (2个)
- `add_text_segment` - 添加字幕片段
- `add_text_animation_effect` - 添加字幕动画特效

#### 音频片段管理 (4个)
- `add_audio_segment` - 添加音频片段
- `add_audio_keyframes` - 添加音频关键帧
- `add_audio_fade_effect` - 添加淡入淡出特效
- `add_audio_effects` - 添加音频特效

#### 草稿管理 (2个)
- `create_draft` - 创建新草稿
- `export_draft` - 导出草稿为zip

#### 素材工具 (1个)
- `get_media_info` - 获取媒体文件信息

## 📁 项目结构

```
JianYingDraft_MCP/
├── main.py                    # MCP服务器主文件
├── test_tools.py              # 工具测试脚本
├── pyproject.toml             # 项目配置
├── README.md                  # 项目说明
├── openapi-documentation.md   # 剪映API文档
├── tools/                     # 工具模块目录
│   ├── __init__.py           # 包初始化
│   ├── track_tools.py        # 轨道管理工具
│   ├── video_tools.py        # 视频片段工具
│   ├── text_tools.py         # 字幕片段工具
│   ├── audio_tools.py        # 音频片段工具
│   ├── draft_tools.py        # 草稿管理工具
│   └── material_tools.py     # 素材工具
└── MCP Document/             # MCP文档
    ├── 1_Introduction.md
    ├── 2_Architecture Overview.md
    └── 3_mcp python sdk.md
```

## 🔧 安装和运行

### 1. 环境要求
- Python 3.13+
- uv (推荐) 或 pip

### 2. 安装依赖
```bash
# 使用 uv (推荐)
uv sync

# 或使用 pip
pip install -e .
```

### 3. 启动服务器
```bash
# 直接运行
python server.py

# 或使用 uv
uv run python server.py
```

### 4. 测试工具
```bash
# 测试所有工具注册
python test_tools.py

# 使用 MCP Inspector 调试
uv run mcp dev server.py
```

## 🎯 使用示例

### 创建完整的视频项目
```python
# 1. 创建草稿
draft_result = await session.call_tool("create_draft", {
    "width": 1920,
    "height": 1080,
    "fps": 30,
    "draft_path": "/path/to/draft",
    "name": "我的视频项目"
})

# 2. 添加视频轨道
track_result = await session.call_tool("add_track", {
    "draft_id": draft_id,
    "track_type": "video",
    "track_name": "主视频轨道"
})

# 3. 添加视频片段
video_result = await session.call_tool("add_video_segment", {
    "draft_id": draft_id,
    "resource_path": "/path/to/video.mp4",
    "target_start": "0",
    "target_duration": "10000000"
})

# 4. 添加字幕
text_result = await session.call_tool("add_text_segment", {
    "draft_id": draft_id,
    "text": "欢迎观看我的视频",
    "target_start": "0",
    "target_duration": "3000000"
})

# 5. 导出项目
export_result = await session.call_tool("export_draft", {
    "draft_id": draft_id
})
```

### 添加视频特效
```python
# 添加滤镜
filter_result = await session.call_tool("add_video_filters", {
    "draft_id": draft_id,
    "segment_id": segment_id,
    "filters": [{
        "filterType": {
            "resourceId": "filter_001",
            "resourceName": "美颜滤镜"
        },
        "intensity": 0.8
    }]
})

# 添加转场
transition_result = await session.call_tool("add_video_transition", {
    "draft_id": draft_id,
    "video_segment_id": segment_id,
    "transition_resource_id": "transition_001",
    "transition_resource_name": "淡入淡出",
    "real_duration": 1000000
})
```

## ⚙️ 配置说明

### API服务器配置
确保剪映API服务运行在: `http://localhost:81/api/jy`

### 工具参数说明
每个工具都有详细的参数说明和类型检查：
- **必需参数**: 标记为必需的参数必须提供
- **可选参数**: 有默认值或可以为空的参数
- **类型验证**: 自动进行参数类型验证
- **错误处理**: 完整的错误处理和响应

## 🔍 调试和测试

### 使用MCP Inspector
```bash
uv run mcp dev server.py
```
这将启动MCP Inspector，提供Web界面来测试所有工具。

### 查看工具列表
运行测试脚本查看所有可用工具：
```bash
python test_tools.py
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License