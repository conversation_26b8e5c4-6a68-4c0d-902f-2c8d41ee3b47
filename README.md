# JianYingDraft MCP Server

一个基于 Model Context Protocol (MCP) 的剪映草稿管理服务器示例。

## 什么是 MCP？

Model Context Protocol (MCP) 是一个开放协议，用于标准化应用程序如何为大语言模型(LLM)提供上下文。它就像AI应用的"USB-C接口"，为AI模型连接不同数据源和工具提供标准化方式。

### MCP 核心概念

1. **架构**: 客户端-服务器架构
   - **MCP Host**: AI应用（如Claude Desktop）
   - **MCP Client**: 维护与MCP服务器连接的组件
   - **MCP Server**: 提供上下文数据的程序

2. **三个核心原语**:
   - **Tools**: 可执行的函数，AI可以调用来执行操作
   - **Resources**: 数据源，为AI提供上下文信息
   - **Prompts**: 可重用的模板，帮助构建与语言模型的交互

## 项目功能

这个示例MCP服务器提供了剪映草稿管理的基本功能：

### Tools (工具)
- `create_draft`: 创建新的剪映草稿
- `get_draft_info`: 获取指定草稿的详细信息
- `list_all_drafts`: 列出所有草稿

### Resources (资源)
- `jianyingdraft://drafts/{draft_id}`: 获取特定草稿的数据
- `jianyingdraft://stats`: 获取统计信息

### Prompts (提示模板)
- `video_script_prompt`: 生成视频脚本的提示模板
- `edit_suggestion_prompt`: 生成编辑建议的提示模板

## 安装和运行

### 1. 安装依赖

```bash
# 使用 uv (推荐)
uv sync

# 或使用 pip
pip install -e .
```

### 2. 运行服务器

```bash
# 直接运行
python main.py

# 或使用 uv
uv run python main.py
```

### 3. 测试服务器

```bash
# 运行测试客户端
python test_mcp_client.py

# 或使用 uv
uv run python test_mcp_client.py
```

### 4. 使用 MCP Inspector 调试

```bash
# 使用 MCP Inspector 进行交互式调试
uv run mcp dev main.py
```

## 使用示例

### 创建草稿
```python
# 通过工具创建新草稿
result = await session.call_tool(
    "create_draft",
    arguments={"title": "我的视频", "description": "描述"}
)
```

### 获取资源
```python
# 读取统计信息
stats = await session.read_resource(AnyUrl("jianyingdraft://stats"))

# 读取特定草稿
draft = await session.read_resource(AnyUrl("jianyingdraft://drafts/draft_001"))
```

### 使用提示模板
```python
# 获取视频脚本提示
prompt = await session.get_prompt(
    "video_script_prompt",
    arguments={"topic": "编程教程", "style": "educational"}
)
```

## 项目结构

```
JianYingDraft_MCP/
├── main.py                 # MCP服务器主文件
├── test_mcp_client.py      # 测试客户端
├── pyproject.toml          # 项目配置
├── README.md               # 项目说明
└── MCP Document/           # MCP文档
    ├── 1_Introduction.md
    ├── 2_Architecture Overview.md
    └── 3_mcp python sdk.md
```

## 扩展功能

你可以基于这个示例扩展更多功能：

1. **更多工具**: 添加视频编辑、导出等功能
2. **数据持久化**: 使用数据库存储草稿数据
3. **文件操作**: 处理视频文件的上传和管理
4. **AI集成**: 集成视频分析和自动编辑功能

## 学习资源

- [MCP 官方文档](https://modelcontextprotocol.io)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [MCP 规范](https://spec.modelcontextprotocol.io)

## 许可证

MIT License