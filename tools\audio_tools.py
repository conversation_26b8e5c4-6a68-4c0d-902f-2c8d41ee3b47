"""
音频片段管理工具
包含音频片段相关的所有API工具
"""

import requests
from typing import Dict, Any, Optional, List
from mcp.server.fastmcp import FastMCP
from .config import config
import json

def register_audio_tools(mcp: FastMCP):
    """注册音频片段管理工具到MCP服务器"""
    
    @mcp.tool()
    def add_audio_segment(
        draft_id: str,
        resource_path: str,
        speed: float = 1.0,
        volume: float = 1.0,
        target_start: str = "0",
        target_duration: str = "1000000",
        after_segment_id: Optional[str] = None,
        source_start: Optional[str] = None,
        source_duration: Optional[str] = None,
        track_id: Optional[str] = None,
        clip_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        添加音频片段到草稿
        
        参数:
        - draft_id (str): 草稿ID，必需
        - resource_path (str): 音频素材路径或网络路径，必需
        - speed (float): 播放速度，默认1.0
        - volume (float): 音量，默认1.0
        - target_start (str): 目标时间范围起始时间，默认"0"
        - target_duration (str): 目标时间范围持续时间，默认"1000000"
        - after_segment_id (str, 可选): 添加到哪个片段之后
        - source_start (str, 可选): 截取音频的起始时间
        - source_duration (str, 可选): 截取音频的持续时间
        - track_id (str, 可选): 轨道ID
        - clip_settings (dict, 可选): 音频片段配置项
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和音频片段信息
        """
        
        # 构建请求数据
        request_data = {
            "draftId": draft_id,
            "resourcePath": resource_path,
            "speed": speed,
            "volume": volume,
            "targetTimerange": {
                "start": target_start,
                "duration": target_duration
            }
        }
        
        # 添加可选参数
        if after_segment_id:
            request_data["afterSegmentId"] = after_segment_id
            
        if source_start and source_duration:
            request_data["sourceTimerange"] = {
                "start": source_start,
                "duration": source_duration
            }
            
        if track_id:
            request_data["trackId"] = track_id
            
        if clip_settings:
            request_data["clipSettings"] = clip_settings
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/audio/add",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "音频片段添加成功",
                    "data": result,
                    "audio_info": {
                        "draft_id": draft_id,
                        "resource_path": resource_path,
                        "speed": speed,
                        "volume": volume,
                        "target_timerange": f"{target_start}-{target_duration}"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"音频片段添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加音频片段时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_audio_keyframes(
        draft_id: str,
        audio_segment_id: str,
        keyframes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        为音频片段批量添加关键帧
        
        参数:
        - draft_id (str): 草稿ID，必需
        - audio_segment_id (str): 音频片段ID，必需
        - keyframes (List[Dict]): 关键帧列表，每个关键帧包含timeOffset和volume
          格式: [{"timeOffset": "1000000", "volume": 0.8}, {"timeOffset": "2000000", "volume": 0.5}]
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和关键帧信息
        """
        
        request_data = {
            "draftId": draft_id,
            "audioSegmentId": audio_segment_id,
            "keyframes": keyframes
        }
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/audio/add-keyframe",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "音频关键帧添加成功",
                    "data": result,
                    "keyframe_info": {
                        "draft_id": draft_id,
                        "audio_segment_id": audio_segment_id,
                        "keyframe_count": len(keyframes),
                        "keyframes": keyframes
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"音频关键帧添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加音频关键帧时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_audio_fade_effect(
        draft_id: str,
        audio_segment_id: str,
        in_duration: Optional[str] = None,
        out_duration: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        为音频片段添加或更新淡入淡出特效
        
        参数:
        - draft_id (str): 草稿ID，必需
        - audio_segment_id (str): 音频片段ID，必需
        - in_duration (str, 可选): 淡入时间
        - out_duration (str, 可选): 淡出时间
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和淡入淡出信息
        """
        
        audio_fade = {}
        if in_duration:
            audio_fade["inDuration"] = in_duration
        if out_duration:
            audio_fade["outDuration"] = out_duration
        
        request_data = {
            "draftId": draft_id,
            "audioSegmentId": audio_segment_id,
            "audioFade": audio_fade
        }
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/audio/add-fade-effect",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "音频淡入淡出特效添加成功",
                    "data": result,
                    "fade_info": {
                        "draft_id": draft_id,
                        "audio_segment_id": audio_segment_id,
                        "in_duration": in_duration,
                        "out_duration": out_duration
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"音频淡入淡出特效添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加音频淡入淡出特效时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_audio_effects(
        draft_id: str,
        segment_id: str,
        audio_effects: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        为音频片段添加音频特效
        
        参数:
        - draft_id (str): 草稿ID，必需
        - segment_id (str): 音频片段ID，必需
        - audio_effects (List[Dict]): 音频特效列表，每个特效包含effectType和params
          格式: [{"effectType": {"resourceId": "id", "resourceName": "name"}, "params": [1.0, 2.0]}]
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和音频特效信息
        """
        
        request_data = {
            "draftId": draft_id,
            "segmentId": segment_id,
            "audioEffects": audio_effects
        }
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/audio/add-effects",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "音频特效添加成功",
                    "data": result,
                    "effect_info": {
                        "draft_id": draft_id,
                        "segment_id": segment_id,
                        "effect_count": len(audio_effects),
                        "effects": [e["effectType"]["resourceName"] for e in audio_effects]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"音频特效添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加音频特效时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    print("音频片段管理工具注册完成: add_audio_segment, add_audio_keyframes, add_audio_fade_effect, add_audio_effects")
