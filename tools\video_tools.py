"""
视频片段管理工具
包含视频片段相关的所有API工具
"""

import requests
from typing import Dict, Any, Optional, List
from mcp.server.fastmcp import FastMCP
from .config import config
import json

def register_video_tools(mcp: FastMCP):
    """注册视频片段管理工具到MCP服务器"""
    
    @mcp.tool()
    def add_video_segment(
        draft_id: str,
        resource_path: str,
        speed: float = 1.0,
        volume: float = 1.0,
        target_start: str = "0",
        target_duration: str = "1000000",
        after_segment_id: Optional[str] = None,
        source_start: Optional[str] = None,
        source_duration: Optional[str] = None,
        track_id: Optional[str] = None,
        clip_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        添加视频片段到草稿
        
        参数:
        - draft_id (str): 草稿ID，必需
        - resource_path (str): 素材路径或网络路径，必需
        - speed (float): 播放速度，默认1.0
        - volume (float): 音量，默认1.0
        - target_start (str): 目标时间范围起始时间，默认"0"
        - target_duration (str): 目标时间范围持续时间，默认"1000000"
        - after_segment_id (str, 可选): 添加到哪个片段之后
        - source_start (str, 可选): 截取素材的起始时间
        - source_duration (str, 可选): 截取素材的持续时间
        - track_id (str, 可选): 轨道ID
        - clip_settings (dict, 可选): 裁剪设置
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和片段信息
        """
        
        # 构建请求数据
        request_data = {
            "draftId": draft_id,
            "resourcePath": resource_path,
            "speed": speed,
            "volume": volume,
            "targetTimerange": {
                "start": target_start,
                "duration": target_duration
            }
        }
        
        # 添加可选参数
        if after_segment_id:
            request_data["afterSegmentId"] = after_segment_id
            
        if source_start and source_duration:
            request_data["sourceTimerange"] = {
                "start": source_start,
                "duration": source_duration
            }
            
        if track_id:
            request_data["trackId"] = track_id
            
        if clip_settings:
            request_data["clipSettings"] = clip_settings
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频片段添加成功",
                    "data": result,
                    "segment_info": {
                        "draft_id": draft_id,
                        "resource_path": resource_path,
                        "speed": speed,
                        "volume": volume,
                        "target_timerange": f"{target_start}-{target_duration}"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"视频片段添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加视频片段时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_video_transition(
        draft_id: str,
        video_segment_id: str,
        transition_resource_id: str,
        transition_resource_name: str,
        real_duration: int,
        duration: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        给视频片段添加转场特效
        
        参数:
        - draft_id (str): 草稿ID，必需
        - video_segment_id (str): 视频片段ID，必需
        - transition_resource_id (str): 转场资源ID，必需
        - transition_resource_name (str): 转场资源名称，必需
        - real_duration (int): 实际转场持续时间，必需
        - duration (str, 可选): 转场持续时间（微秒）
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和转场信息
        """
        
        request_data = {
            "draftId": draft_id,
            "videoSegmentId": video_segment_id,
            "transitionType": {
                "resourceId": transition_resource_id,
                "resourceName": transition_resource_name
            },
            "realDuration": real_duration
        }
        
        if duration:
            request_data["duration"] = duration
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add-transition",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频转场特效添加成功",
                    "data": result,
                    "transition_info": {
                        "draft_id": draft_id,
                        "video_segment_id": video_segment_id,
                        "transition_type": transition_resource_name,
                        "duration": real_duration
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"转场特效添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加转场特效时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_video_mask(
        draft_id: str,
        segment_id: str,
        mask_resource_id: str,
        mask_resource_name: str,
        center_x: float = 0.5,
        center_y: float = 0.5,
        size: float = 0.5,
        rotation: float = 0.0,
        feather: float = 0.0,
        invert: bool = False,
        rect_width: Optional[float] = None,
        round_corner: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        为视频片段添加蒙版
        
        参数:
        - draft_id (str): 草稿ID，必需
        - segment_id (str): 片段ID，必需
        - mask_resource_id (str): 蒙版资源ID，必需
        - mask_resource_name (str): 蒙版资源名称，必需
        - center_x (float): 蒙版中心点X坐标，默认0.5
        - center_y (float): 蒙版中心点Y坐标，默认0.5
        - size (float): 蒙版主要尺寸，默认0.5
        - rotation (float): 蒙版旋转角度，默认0.0
        - feather (float): 蒙版羽化参数(0~100)，默认0.0
        - invert (bool): 是否反转蒙版，默认False
        - rect_width (float, 可选): 矩形蒙版宽度
        - round_corner (float, 可选): 矩形蒙版圆角参数
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和蒙版信息
        """
        
        request_data = {
            "draftId": draft_id,
            "segmentId": segment_id,
            "maskType": {
                "resourceId": mask_resource_id,
                "resourceName": mask_resource_name
            },
            "centerX": center_x,
            "centerY": center_y,
            "size": size,
            "rotation": rotation,
            "feather": feather,
            "invert": invert
        }
        
        if rect_width is not None:
            request_data["rectWidth"] = rect_width
        if round_corner is not None:
            request_data["roundCorner"] = round_corner
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add-mask",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频蒙版添加成功",
                    "data": result,
                    "mask_info": {
                        "draft_id": draft_id,
                        "segment_id": segment_id,
                        "mask_type": mask_resource_name,
                        "center": f"({center_x}, {center_y})",
                        "size": size,
                        "rotation": rotation
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"视频蒙版添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加视频蒙版时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_video_filters(
        draft_id: str,
        segment_id: str,
        filters: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        为视频片段添加滤镜

        参数:
        - draft_id (str): 草稿ID，必需
        - segment_id (str): 片段ID，必需
        - filters (List[Dict]): 滤镜列表，每个滤镜包含filterType和intensity
          格式: [{"filterType": {"resourceId": "id", "resourceName": "name"}, "intensity": 0.5}]

        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和滤镜信息
        """

        request_data = {
            "draftId": draft_id,
            "segmentId": segment_id,
            "filters": filters
        }

        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add-filters",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频滤镜添加成功",
                    "data": result,
                    "filter_info": {
                        "draft_id": draft_id,
                        "segment_id": segment_id,
                        "filter_count": len(filters),
                        "filters": [f["filterType"]["resourceName"] for f in filters]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"视频滤镜添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }

        except Exception as e:
            return {
                "success": False,
                "message": "添加视频滤镜时发生错误",
                "error": str(e),
                "request_data": request_data
            }

    @mcp.tool()
    def add_video_effects(
        draft_id: str,
        segment_id: str,
        effects: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        为视频片段添加特效

        参数:
        - draft_id (str): 草稿ID，必需
        - segment_id (str): 片段ID，必需
        - effects (List[Dict]): 特效列表，每个特效包含effectType和params
          格式: [{"effectType": {"resourceId": "id", "resourceName": "name"}, "params": [1.0, 2.0]}]

        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和特效信息
        """

        request_data = {
            "draftId": draft_id,
            "segmentId": segment_id,
            "effects": effects
        }

        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add-effects",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频特效添加成功",
                    "data": result,
                    "effect_info": {
                        "draft_id": draft_id,
                        "segment_id": segment_id,
                        "effect_count": len(effects),
                        "effects": [e["effectType"]["resourceName"] for e in effects]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"视频特效添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }

        except Exception as e:
            return {
                "success": False,
                "message": "添加视频特效时发生错误",
                "error": str(e),
                "request_data": request_data
            }

    @mcp.tool()
    def add_video_background_filling(
        draft_id: str,
        video_segment_id: str,
        fill_type: str,
        blur: float = 0.0,
        color: str = "#000000"
    ) -> Dict[str, Any]:
        """
        给视频片段添加背景填充特效

        参数:
        - draft_id (str): 草稿ID，必需
        - video_segment_id (str): 视频片段ID，必需
        - fill_type (str): 背景填充类型，必需
        - blur (float): 模糊度(0~1)，默认0.0
        - color (str): 填充颜色，默认"#000000"

        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和背景填充信息
        """

        request_data = {
            "draftId": draft_id,
            "videoSegmentId": video_segment_id,
            "fillType": fill_type,
            "blur": blur,
            "color": color
        }

        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add-background-filling",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频背景填充添加成功",
                    "data": result,
                    "filling_info": {
                        "draft_id": draft_id,
                        "video_segment_id": video_segment_id,
                        "fill_type": fill_type,
                        "blur": blur,
                        "color": color
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"视频背景填充添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }

        except Exception as e:
            return {
                "success": False,
                "message": "添加视频背景填充时发生错误",
                "error": str(e),
                "request_data": request_data
            }

    @mcp.tool()
    def add_video_animation(
        draft_id: str,
        video_segment_id: str,
        animation_resource_id: str,
        animation_resource_name: str,
        duration: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        给视频片段添加动画效果

        参数:
        - draft_id (str): 草稿ID，必需
        - video_segment_id (str): 视频片段ID，必需
        - animation_resource_id (str): 动画资源ID，必需
        - animation_resource_name (str): 动画资源名称，必需
        - duration (str, 可选): 动画时间范围

        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和动画信息
        """

        request_data = {
            "draftId": draft_id,
            "videoSegmentId": video_segment_id,
            "type": {
                "resourceId": animation_resource_id,
                "resourceName": animation_resource_name
            }
        }

        if duration:
            request_data["duration"] = duration

        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/video/add-animation",
                data=json.dumps(request_data),
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "视频动画添加成功",
                    "data": result,
                    "animation_info": {
                        "draft_id": draft_id,
                        "video_segment_id": video_segment_id,
                        "animation_type": animation_resource_name,
                        "duration": duration
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"视频动画添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }

        except Exception as e:
            return {
                "success": False,
                "message": "添加视频动画时发生错误",
                "error": str(e),
                "request_data": request_data
            }

    print("视频片段管理工具注册完成: add_video_segment, add_video_transition, add_video_mask, add_video_filters, add_video_effects, add_video_background_filling, add_video_animation")
