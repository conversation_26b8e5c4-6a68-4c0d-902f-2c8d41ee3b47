# API 文档

## 基本信息

- **OpenAPI 版本**: 3.1.0
- **标题**: OpenAPI definition
- **版本**: v0
- **服务器地址**: http://localhost:81/api/jy

## 标签分类

1. **字幕片段管理**: 字幕片段相关接口
2. **素材工具接口**: 素材工具接口
3. **视频片段管理**: 视频片段相关接口
4. **草稿管理**: 草稿管理相关接口
5. **音频片段管理**: 音频片段相关接口
6. **轨道管理**: 轨道相关接口

---

## 轨道管理

### 添加轨道
```
POST /track/add
```

**描述**: 添加一个新的轨道到MongoDB

**请求体**:
```json
{
  "draftId": "string",
  "trackType": "string",
  "trackName": "string",
  "mute": boolean,
  "relativeIndex": integer,
  "absoluteIndex": integer
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `trackType` (必需): 轨道类型
- `trackName`: 轨道名称（仅在创建第一个同类型轨道时允许不指定）
- `mute` (必需): 轨道是否静音（默认不静音）
- `relativeIndex` (必需): 相对(同类型轨道的)图层位置（越高越接近前景，默认为0）
- `absoluteIndex`: 绝对图层位置（此参数不能与relative_index同时使用）

---

## 视频片段管理

### 添加视频片段
```
POST /segment/video/add
```

**描述**: 添加一个新的视频片段到MongoDB

**请求体**:
```json
{
  "afterSegmentId": "string",
  "draftId": "string",
  "targetTimerange": {
    "start": "string",
    "duration": "string"
  },
  "sourceTimerange": {
    "start": "string",
    "duration": "string"
  },
  "speed": number,
  "volume": number,
  "resourcePath": "string",
  "trackId": "string",
  "clipSettings": {
    "alpha": number,
    "flipHorizontal": boolean,
    "flipVertical": boolean,
    "rotation": number,
    "scaleX": number,
    "scaleY": number,
    "transformX": number,
    "transformY": number
  }
}
```

**参数说明**:
- `draftId` (必需): 素材所属的 draftId
- `resourcePath` (必需): 素材实例或素材路径（网络路径）
- `speed` (必需): 播放速度（默认为1.0）
- `volume` (必需): 音量（默认为1.0）
- `targetTimerange` (必需): 片段在轨道上的目标时间范围
- `afterSegmentId`: 素材片段添加到哪个片段之后
- `sourceTimerange`: 截取的素材片段的时间范围
- `trackId`: 轨道id
- `clipSettings`: 素材片段的配置项

---

### 给视频片段添加转场特效
```
POST /segment/video/add-transition
```

**描述**: 给指定的视频片段添加转场特效

**请求体**:
```json
{
  "draftId": "string",
  "videoSegmentId": "string",
  "transitionType": {
    "resourceId": "string",
    "resourceName": "string"
  },
  "duration": "string",
  "realDuration": integer
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `videoSegmentId` (必需): 视频片段id
- `transitionType` (必需): 转场类型
- `duration`: 转场持续时间（单位为微秒）
- `realDuration` (必需): 读取的转场持续时间

---

### 添加视频蒙版
```
POST /segment/video/add-mask
```

**描述**: 为视频片段添加视频蒙版（同一个片段只能有一个蒙版）

**请求体**:
```json
{
  "draftId": "string",
  "segmentId": "string",
  "maskType": {
    "resourceId": "string",
    "resourceName": "string"
  },
  "centerX": number,
  "centerY": number,
  "size": number,
  "rotation": number,
  "feather": number,
  "invert": boolean,
  "rectWidth": number,
  "roundCorner": number
}
```

**参数说明**:
- `draftId` (必需): 素材所属的 draftId
- `segmentId` (必需): 素材所属的片段Id
- `maskType` (必需): 蒙版类型
- `centerX` (必需): 蒙版中心点X坐标（默认素材中心）
- `centerY` (必需): 蒙版中心点Y坐标（默认素材中心）
- `size` (必需): 蒙版主要尺寸（占素材高度的比例，默认为0.5）
- `rotation` (必需): 蒙版顺时针旋转角度（默认不旋转）
- `feather` (必需): 蒙版的羽化参数（0~100）
- `invert` (必需): 是否反转蒙版（默认不反转）
- `rectWidth`: 矩形蒙版的宽度（仅矩形蒙版有效）
- `roundCorner`: 矩形蒙版的圆角参数（仅矩形蒙版有效）

---

### 添加视频滤镜
```
POST /segment/video/add-filters
```

**描述**: 为视频片段添加视频滤镜（同一类型的滤镜根据resourceId去重）

**请求体**:
```json
{
  "draftId": "string",
  "segmentId": "string",
  "filters": [
    {
      "filterType": {
        "resourceId": "string",
        "resourceName": "string"
      },
      "intensity": number
    }
  ]
}
```

**参数说明**:
- `draftId` (必需): 素材所属的 draftId
- `segmentId` (必需): 素材所属的片段Id
- `filters` (必需): 滤镜参数数组

---

### 添加视频特效
```
POST /segment/video/add-effects
```

**描述**: 为视频片段添加视频特效（同一类型的特效根据resourceId去重）

**请求体**:
```json
{
  "draftId": "string",
  "segmentId": "string",
  "effects": [
    {
      "effectType": {
        "resourceId": "string",
        "resourceName": "string"
      },
      "params": [number]
    }
  ]
}
```

**参数说明**:
- `draftId` (必需): 素材所属的 draftId
- `segmentId` (必需): 素材所属的片段Id
- `effects` (必需): 视频特效参数数组

---

### 给视频片段添加背景填充特效
```
POST /segment/video/add-background-filling
```

**描述**: 给指定的视频片段添加背景填充特效

**请求体**:
```json
{
  "draftId": "string",
  "videoSegmentId": "string",
  "fillType": "string",
  "blur": number,
  "color": "string"
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `videoSegmentId` (必需): 视频片段id
- `fillType` (必需): 背景填充类型
- `blur` (必需): 模糊度（0~1）
- `color` (必需): 填充颜色

---

### 给视频片段添加动画
```
POST /segment/video/add-animation
```

**描述**: 给指定的视频片段添加动画效果

**请求体**:
```json
{
  "type": {
    "resourceId": "string",
    "resourceName": "string"
  },
  "duration": "string",
  "draftId": "string",
  "videoSegmentId": "string"
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `videoSegmentId` (必需): 视频片段id
- `type` (必需): 动画类型
- `duration`: 动画的时间范围

---

## 字幕片段管理

### 添加字幕片段
```
POST /segment/text/add
```

**描述**: 添加一个新的字幕片段到MongoDB

**请求体**:
```json
{
  "afterSegmentId": "string",
  "textSegmentId": "string",
  "draftId": "string",
  "text": "string",
  "font": {
    "resourceId": "string",
    "resourceName": "string"
  },
  "style": {
    "size": number,
    "bold": boolean,
    "italic": boolean,
    "underline": boolean,
    "color": [number],
    "alpha": number,
    "align": integer,
    "vertical": boolean,
    "letterSpacing": integer,
    "lineSpacing": integer,
    "autoWrapping": boolean,
    "maxLineWidth": number
  },
  "border": {
    "alpha": number,
    "color": [number],
    "width": number
  },
  "clipSettings": {
    "alpha": number,
    "flipHorizontal": boolean,
    "flipVertical": boolean,
    "rotation": number,
    "scaleX": number,
    "scaleY": number,
    "transformX": number,
    "transformY": number
  },
  "background": {
    "style": integer,
    "alpha": number,
    "color": "string",
    "roundRadius": number,
    "height": number,
    "width": number,
    "horizontalOffset": number,
    "verticalOffset": number
  },
  "trackId": "string",
  "targetRanger": {
    "start": "string",
    "duration": "string"
  }
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `text` (必需): 文本内容
- `afterSegmentId`: 素材片段添加到哪个片段之后
- `textSegmentId`: 字幕片段id
- `font`: 字体
- `style`: 字体样式
- `border`: 文本描边
- `clipSettings`: 裁剪设置
- `background`: 文本背景
- `trackId`: 轨道id
- `targetRanger`: 目标时间范围

---

### 添加/更新字幕动画和特效
```
POST /segment/text/add-animation-effect
```

**描述**: 为字幕片段添加或更新动画和特效

**请求体**:
```json
{
  "textSegmentId": "string",
  "draftId": "string",
  "type": {
    "resourceId": "string",
    "resourceName": "string"
  },
  "duration": "string",
  "bubbleEffectId": "string",
  "bubbleResourceId": "string",
  "flowerEffectId": "string"
}
```

**参数说明**:
- `textSegmentId` (必需): 字幕片段id
- `draftId` (必需): 草稿id
- `type`: 动画类型
- `duration`: 动画持续时间（单位为秒）
- `bubbleEffectId`: 泡泡特效id
- `bubbleResourceId`: 泡泡特效资源id
- `flowerEffectId`: 花字特效资源id

---

## 音频片段管理

### 添加音频片段
```
POST /segment/audio/add
```

**描述**: 添加一个新的音频片段到MongoDB

**请求体**:
```json
{
  "afterSegmentId": "string",
  "draftId": "string",
  "targetTimerange": {
    "start": "string",
    "duration": "string"
  },
  "sourceTimerange": {
    "start": "string",
    "duration": "string"
  },
  "speed": number,
  "volume": number,
  "resourcePath": "string",
  "trackId": "string",
  "clipSettings": {
    "alpha": number,
    "flipHorizontal": boolean,
    "flipVertical": boolean,
    "rotation": number,
    "scaleX": number,
    "scaleY": number,
    "transformX": number,
    "transformY": number
  }
}
```

**参数说明**:
- `draftId` (必需): 素材所属的 draftId
- `resourcePath` (必需): 素材实例或素材路径（网络路径）
- `speed` (必需): 播放速度（默认为1.0）
- `volume` (必需): 音量（默认为1.0）
- `targetTimerange` (必需): 片段在轨道上的目标时间范围
- `afterSegmentId`: 素材片段添加到哪个片段之后
- `sourceTimerange`: 截取的素材片段的时间范围
- `trackId`: 轨道id
- `clipSettings`: 素材片段的配置项

---

### 批量添加音频关键帧
```
POST /segment/audio/add-keyframe
```

**描述**: 为音频片段批量添加关键帧信息（同一片段的timeOffset和volume组合不能重复）

**请求体**:
```json
{
  "draftId": "string",
  "audioSegmentId": "string",
  "keyframes": [
    {
      "timeOffset": "string",
      "volume": number
    }
  ]
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `audioSegmentId` (必需): 音频片段id
- `keyframes` (必需): 关键帧列表

---

### 添加/更新音频淡入淡出特效
```
POST /segment/audio/add-fade-effect
```

**描述**: 为音频片段添加或更新淡入淡出特效

**请求体**:
```json
{
  "draftId": "string",
  "audioSegmentId": "string",
  "audioFade": {
    "inDuration": "string",
    "outDuration": "string"
  }
}
```

**参数说明**:
- `draftId` (必需): 草稿id
- `audioSegmentId` (必需): 音频片段id
- `audioFade` (必需): 音频淡入淡出效果

---

### 添加音频特效
```
POST /segment/audio/add-effects
```

**描述**: 为音频片段添加音频特效（同一类型的特效根据resourceId去重）

**请求体**:
```json
{
  "draftId": "string",
  "segmentId": "string",
  "audioEffects": [
    {
      "effectType": {
        "resourceId": "string",
        "resourceName": "string"
      },
      "params": [number]
    }
  ]
}
```

**参数说明**:
- `draftId` (必需): 素材所属的 draftId
- `segmentId` (必需): 素材片段的id
- `audioEffects` (必需): 音频素材片段特效

---

## 草稿管理

### 创建草稿
```
POST /draft/create
```

**描述**: 创建一个新的草稿

**请求头**:
- `apiKey` (必需): string

**请求体**:
```json
{
  "width": integer,
  "height": integer,
  "fps": integer,
  "name": "string",
  "draftPath": "string",
  "apiKey": "string"
}
```

**参数说明**:
- `width` (必需): 草稿宽度
- `height` (必需): 草稿高度
- `fps` (必需): 帧率
- `draftPath` (必需): 草稿路径
- `name`: 草稿名称
- `apiKey`: apiKey

**响应示例**:
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "draftId": "string",
    "draftName": "string",
    "draftContent": {}
  }
}
```

---

### 导出草稿为zip
```
GET /draft/{draftId}/export
```

**描述**: 导出草稿及其所有资源文件为一个zip压缩包

**路径参数**:
- `draftId` (必需): 草稿ID

---

## 素材工具接口

### 获取素材信息
```
GET /materials/utils/media-info
```

**描述**: 获取素材信息（视频,音频,图片）

**查询参数**:
- `filePath` (必需): string

**响应示例**:
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "fileName": "string",
    "absolutePath": "string",
    "fileSize": integer,
    "mimeType": "string",
    "type": "string",
    "width": integer,
    "height": integer,
    "durationMicroseconds": integer,
    "durationSeconds": "string"
  }
}
```

---

## 数据模型

### TrackAddReqDto
- **类型**: object
- **描述**: 轨道添加请求参数
- **属性**:
  - `draftId` (string): 草稿id
  - `trackType` (string): 轨道类型
  - `trackName` (string): 轨道名称
  - `mute` (boolean): 轨道是否静音
  - `relativeIndex` (integer): 相对图层位置
  - `absoluteIndex` (integer): 绝对图层位置

### DataResponseString
- **类型**: object
- **描述**: 统一响应实体
- **属性**:
  - `code` (integer): 响应码（200为成功）
  - `message` (string): 响应消息
  - `data` (string): 响应数据体

### ClipSettings
- **类型**: object
- **描述**: 素材片段的图像调节设置
- **属性**:
  - `alpha` (number): 图像不透明度（0-1）
  - `flipHorizontal` (boolean): 是否水平翻转
  - `flipVertical` (boolean): 是否垂直翻转
  - `rotation` (number): 顺时针旋转的角度
  - `scaleX` (number): 水平缩放比例
  - `scaleY` (number): 垂直缩放比例
  - `transformX` (number): 水平位移（单位为半个画布宽）
  - `transformY` (number): 垂直位移（单位为半个画布高）

### MediaSegmentAddReqDto
- **类型**: object
- **描述**: 音频/视频片段添加请求参数
- **属性**:
  - `afterSegmentId` (string): 素材片段添加到哪个片段之后
  - `draftId` (string): 素材所属的 draftId
  - `targetTimerange` (Timerange): 片段在轨道上的目标时间范围
  - `sourceTimerange` (Timerange): 截取的素材片段的时间范围
  - `speed` (number): 播放速度
  - `volume` (number): 音量
  - `resourcePath` (string): 素材实例或素材路径
  - `trackId` (string): 轨道id
  - `clipSettings` (ClipSettings): 素材片段的配置项

### Timerange
- **类型**: object
- **描述**: 时间范围
- **属性**:
  - `start` (string): 起始时间
  - `duration` (string): 持续长度

### Resource
- **类型**: object
- **描述**: 资源信息
- **属性**:
  - `resourceId` (string): 资源id
  - `resourceName` (string): 资源名字

### TransitionTypeReqDto
- **类型**: object
- **描述**: 转场特效请求参数
- **属性**:
  - `draftId` (string): 草稿id
  - `videoSegmentId` (string): 视频片段id
  - `transitionType` (Resource): 转场类型
  - `duration` (string): 转场持续时间
  - `realDuration` (integer): 读取的转场持续时间

### VideoMaskReqDto
- **类型**: object
- **描述**: 视频蒙版类
- **属性**:
  - `draftId` (string): 素材所属的 draftId
  - `segmentId` (string): 素材所属的片段Id
  - `maskType` (Resource): 蒙版类型
  - `centerX` (number): 蒙版中心点X坐标
  - `centerY` (number): 蒙版中心点Y坐标
  - `size` (number): 蒙版的主要尺寸
  - `rotation` (number): 蒙版顺时针旋转的角度
  - `feather` (number): 蒙版的羽化参数
  - `invert` (boolean): 是否反转蒙版
  - `rectWidth` (number): 矩形蒙版的宽度
  - `roundCorner` (number): 矩形蒙版的圆角参数

### VideoFilter
- **类型**: object
- **描述**: 视频滤镜类
- **属性**:
  - `filterType` (Resource): 滤镜类型
  - `intensity` (number): 滤镜强度

### VideoFilterReqDto
- **类型**: object
- **描述**: 视频滤镜类
- **属性**:
  - `draftId` (string): 素材所属的 draftId
  - `segmentId` (string): 素材所属的片段Id
  - `filters` (array): 滤镜参数列表

### VideoEffect
- **类型**: object
- **描述**: 视频特效类
- **属性**:
  - `effectType` (Resource): 特效类型
  - `params` (array): 特效参数列表

### VideoEffectReqDto
- **类型**: object
- **描述**: 视频特效类
- **属性**:
  - `draftId` (string): 素材所属的 draftId
  - `segmentId` (string): 素材所属的片段Id
  - `effects` (array): 视频特效参数

### BackgroundFillingReqDto
- **类型**: object
- **描述**: 背景填充请求参数
- **属性**:
  - `draftId` (string): 草稿id
  - `videoSegmentId` (string): 视频片段id
  - `fillType` (string): 背景填充类型
  - `blur` (number): 模糊度（0~1）
  - `color` (string): 填充颜色

### VideoAnimationReqDto
- **类型**: object
- **描述**: 视频特效请求参数
- **属性**:
  - `type` (Resource): 动画类型
  - `duration` (string): 动画的时间范围
  - `draftId` (string): 草稿id
  - `videoSegmentId` (string): 视频片段id

### TextBackground
- **类型**: object
- **描述**: 字体背景设置
- **属性**:
  - `style` (integer): 背景样式
  - `alpha` (number): 背景不透明度
  - `color` (string): 背景颜色
  - `roundRadius` (number): 背景圆角半径
  - `height` (number): 背景高度
  - `width` (number): 背景宽度
  - `horizontalOffset` (number): 背景水平偏移
  - `verticalOffset` (number): 背景竖直偏移

### TextBorder
- **类型**: object
- **描述**: 文本描边参数
- **属性**:
  - `alpha` (number): 描边不透明度
  - `color` (array): 描边颜色
  - `width` (number): 描边宽度

### TextSegmentAddReqDto
- **类型**: object
- **描述**: 字幕特效请求参数
- **属性**:
  - `afterSegmentId` (string): 素材片段添加到哪个片段之后
  - `textSegmentId` (string): 字幕片段id
  - `draftId` (string): 草稿id
  - `text` (string): 文本
  - `font` (Resource): 字体
  - `style` (TextStyle): 样式
  - `border` (TextBorder): 边框
  - `clipSettings` (ClipSettings): 裁剪设置
  - `background` (TextBackground): 背景
  - `trackId` (string): 轨道id
  - `targetRanger` (Timerange): 目标时间范围

### TextStyle
- **类型**: object
- **描述**: 字体样式类
- **属性**:
  - `size` (number): 字体大小
  - `bold` (boolean): 是否加粗
  - `italic` (boolean): 是否斜体
  - `underline` (boolean): 是否加下划线
  - `color` (array): 字体颜色
  - `alpha` (number): 字体不透明度
  - `align` (integer): 对齐方式
  - `vertical` (boolean): 是否为竖排文本
  - `letterSpacing` (integer): 字符间距
  - `lineSpacing` (integer): 行间距
  - `autoWrapping` (boolean): 是否自动换行
  - `maxLineWidth` (number): 最大行宽

### TextAnimationAndEffectReqDto
- **类型**: object
- **描述**: 字幕动画和特效请求参数
- **属性**:
  - `textSegmentId` (string): 字幕片段id
  - `draftId` (string): 草稿id
  - `type` (Resource): 动画类型
  - `duration` (string): 动画持续时间
  - `bubbleEffectId` (string): 泡泡特效id
  - `bubbleResourceId` (string): 泡泡特效资源id
  - `flowerEffectId` (string): 花字特效资源id

### AudioKeyframeReqDto
- **类型**: object
- **描述**: 音频关键帧请求参数
- **属性**:
  - `draftId` (string): 草稿id
  - `audioSegmentId` (string): 音频片段id
  - `keyframes` (array): 关键帧列表

### KeyframeData
- **类型**: object
- **描述**: 单个关键帧数据
- **属性**:
  - `timeOffset` (string): 关键帧的时间偏移量
  - `volume` (number): 音量值

### AudioFadeEffect
- **类型**: object
- **描述**: 音频淡入淡出特效
- **属性**:
  - `inDuration` (string): 淡入时间
  - `outDuration` (string): 淡出时间

### AudioFadeEffectReqDto
- **类型**: object
- **描述**: 音频淡入淡出特效请求参数
- **属性**:
  - `draftId` (string): 草稿id
  - `audioSegmentId` (string): 音频片段id
  - `audioFade` (AudioFadeEffect): 音频淡入淡出效果

### AudioEffect
- **类型**: object
- **描述**: 音频特效
- **属性**:
  - `effectType` (Resource): 音效类型
  - `params` (array): 音效参数

### AudioEffectReqDto
- **类型**: object
- **描述**: 音频特效请求参数
- **属性**:
  - `draftId` (string): 素材所属的 draftId
  - `segmentId` (string): 素材片段的id
  - `audioEffects` (array): 音频素材片段特效

### DraftCreateReqDto
- **类型**: object
- **描述**: 创建草稿请求参数
- **属性**:
  - `width` (integer): 草稿宽度
  - `height` (integer): 草稿高度
  - `fps` (integer): 帧率
  - `name` (string): 草稿名称
  - `draftPath` (string): 草稿路径
  - `apiKey` (string): apiKey

### DataResponseDraftCreateRepDto
- **类型**: object
- **描述**: 统一响应实体
- **属性**:
  - `code` (integer): 响应码
  - `message` (string): 响应消息
  - `data` (DraftCreateRepDto): 响应数据体

### DraftCreateRepDto
- **类型**: object
- **描述**: 创建草稿响应实体
- **属性**:
  - `draftId` (string): 草稿id
  - `draftName` (string): 草稿名称
  - `draftContent` (object): 草稿内容

### DataResponseMediaInfo
- **类型**: object
- **描述**: 统一响应实体
- **属性**:
  - `code` (integer): 响应码
  - `message` (string): 响应消息
  - `data` (MediaInfo): 响应数据体

### MediaInfo
- **类型**: object
- **描述**: 媒体文件信息
- **属性**:
  - `fileName` (string): 文件名
  - `absolutePath` (string): 文件路径
  - `fileSize` (integer): 文件大小
  - `mimeType` (string): 文件MIME类型
  - `type` (string): 文件格式
  - `width` (integer): 宽度
  - `height` (integer): 高度
  - `durationMicroseconds` (integer): 时长(微秒)
  - `durationSeconds` (string): 时长(秒)

### DataBuffer
- **类型**: object
- **描述**: 数据缓冲区
