"""
MCP客户端测试脚本
演示如何连接和使用MCP服务器
"""

import asyncio
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from pydantic import AnyUrl

async def test_mcp_server():
    """测试MCP服务器的功能"""
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["main.py"],
        env=os.environ.copy()
    )
    
    print("🚀 连接到 JianYingDraft MCP 服务器...")
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                await session.initialize()
                print("✅ 连接成功！")
                
                # 测试 Tools
                print("\n📋 测试 Tools (工具):")
                
                # 列出所有工具
                tools = await session.list_tools()
                print(f"可用工具: {[tool.name for tool in tools.tools]}")
                
                # 创建新草稿
                print("\n创建新草稿...")
                result = await session.call_tool(
                    "create_draft", 
                    arguments={"title": "测试视频", "description": "这是一个测试视频"}
                )
                print(f"创建结果: {result.content[0].text if result.content else '无内容'}")
                
                # 列出所有草稿
                print("\n列出所有草稿...")
                result = await session.call_tool("list_all_drafts", arguments={})
                print(f"草稿列表: {result.content[0].text if result.content else '无内容'}")
                
                # 获取特定草稿信息
                print("\n获取草稿信息...")
                result = await session.call_tool(
                    "get_draft_info", 
                    arguments={"draft_id": "draft_001"}
                )
                print(f"草稿信息: {result.content[0].text if result.content else '无内容'}")
                
                # 测试 Resources
                print("\n📁 测试 Resources (资源):")
                
                # 列出所有资源
                resources = await session.list_resources()
                print(f"可用资源: {[resource.uri for resource in resources.resources]}")
                
                # 读取统计资源
                print("\n读取统计信息...")
                stats_content = await session.read_resource(AnyUrl("jianyingdraft://stats"))
                if stats_content.contents:
                    print(f"统计信息: {stats_content.contents[0].text}")
                
                # 读取草稿资源
                print("\n读取草稿资源...")
                draft_content = await session.read_resource(AnyUrl("jianyingdraft://drafts/draft_001"))
                if draft_content.contents:
                    print(f"草稿数据: {draft_content.contents[0].text}")
                
                # 测试 Prompts
                print("\n💬 测试 Prompts (提示模板):")
                
                # 列出所有提示
                prompts = await session.list_prompts()
                print(f"可用提示: {[prompt.name for prompt in prompts.prompts]}")
                
                # 获取视频脚本提示
                print("\n获取视频脚本提示...")
                script_prompt = await session.get_prompt(
                    "video_script_prompt",
                    arguments={"topic": "如何学习编程", "style": "educational"}
                )
                if script_prompt.messages:
                    print(f"脚本提示: {script_prompt.messages[0].content.text}")
                
                # 获取编辑建议提示
                print("\n获取编辑建议提示...")
                edit_prompt = await session.get_prompt(
                    "edit_suggestion_prompt",
                    arguments={"draft_title": "我的第一个视频"}
                )
                if edit_prompt.messages:
                    print(f"编辑建议: {edit_prompt.messages[0].content.text}")
                
                print("\n🎉 所有测试完成！")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """运行测试"""
    print("开始测试 JianYingDraft MCP 服务器...")
    asyncio.run(test_mcp_server())

if __name__ == "__main__":
    main()
