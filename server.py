"""
剪映MCP服务器主文件
整合所有剪映API工具
"""

from mcp.server.fastmcp import FastMCP
import os
import sys

# 添加工具模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

# 创建MCP服务器
mcp = FastMCP("剪映MCP服务器")

# 导入所有工具模块
from tools.track_tools import register_track_tools
from tools.video_tools import register_video_tools
from tools.text_tools import register_text_tools
from tools.audio_tools import register_audio_tools
from tools.draft_tools import register_draft_tools
from tools.material_tools import register_material_tools

def main():
    """主函数"""
    print("启动剪映MCP服务器...")
    print("=" * 50)
    
    # 注册所有工具
    print("注册工具模块...")
    register_track_tools(mcp)
    register_video_tools(mcp)
    register_text_tools(mcp)
    register_audio_tools(mcp)
    register_draft_tools(mcp)
    register_material_tools(mcp)
    
    print("所有工具注册完成！")
    print("\n服务器功能:")
    print("- 轨道管理: 添加轨道")
    print("- 视频片段: 添加、转场、蒙版、滤镜、特效、背景填充、动画")
    print("- 字幕片段: 添加字幕、动画特效")
    print("- 音频片段: 添加音频、关键帧、淡入淡出、特效")
    print("- 草稿管理: 创建草稿、导出")
    print("- 素材工具: 获取媒体信息")
    
    print("\n等待客户端连接...")
    
    # 运行服务器
    mcp.run()

if __name__ == "__main__":
    main()
