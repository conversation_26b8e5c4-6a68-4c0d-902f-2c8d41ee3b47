"""
字幕片段管理工具
包含字幕片段相关的所有API工具
"""

import requests
from typing import Dict, Any, Optional, List
from mcp.server.fastmcp import FastMCP
from .config import config

def register_text_tools(mcp: FastMCP):
    """注册字幕片段管理工具到MCP服务器"""
    
    @mcp.tool()
    def add_text_segment(
        draft_id: str,
        text: str,
        target_start: str = "0",
        target_duration: str = "3000000",
        after_segment_id: Optional[str] = None,
        text_segment_id: Optional[str] = None,
        font_resource_id: Optional[str] = None,
        font_resource_name: Optional[str] = None,
        track_id: Optional[str] = None,
        style: Optional[Dict[str, Any]] = None,
        border: Optional[Dict[str, Any]] = None,
        clip_settings: Optional[Dict[str, Any]] = None,
        background: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        添加字幕片段到草稿
        
        参数:
        - draft_id (str): 草稿ID，必需
        - text (str): 文本内容，必需
        - target_start (str): 目标时间范围起始时间，默认"0"
        - target_duration (str): 目标时间范围持续时间，默认"3000000"
        - after_segment_id (str, 可选): 添加到哪个片段之后
        - text_segment_id (str, 可选): 字幕片段ID
        - font_resource_id (str, 可选): 字体资源ID
        - font_resource_name (str, 可选): 字体资源名称
        - track_id (str, 可选): 轨道ID
        - style (dict, 可选): 字体样式设置
        - border (dict, 可选): 文本描边设置
        - clip_settings (dict, 可选): 裁剪设置
        - background (dict, 可选): 文本背景设置
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和字幕信息
        """
        
        # 构建请求数据
        request_data = {
            "draftId": draft_id,
            "text": text,
            "targetRanger": {
                "start": target_start,
                "duration": target_duration
            }
        }
        
        # 添加可选参数
        if after_segment_id:
            request_data["afterSegmentId"] = after_segment_id
            
        if text_segment_id:
            request_data["textSegmentId"] = text_segment_id
            
        if font_resource_id and font_resource_name:
            request_data["font"] = {
                "resourceId": font_resource_id,
                "resourceName": font_resource_name
            }
            
        if track_id:
            request_data["trackId"] = track_id
            
        if style:
            request_data["style"] = style
            
        if border:
            request_data["border"] = border
            
        if clip_settings:
            request_data["clipSettings"] = clip_settings
            
        if background:
            request_data["background"] = background
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/text/add",
                json=request_data,
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "字幕片段添加成功",
                    "data": result,
                    "text_info": {
                        "draft_id": draft_id,
                        "text": text,
                        "target_timerange": f"{target_start}-{target_duration}",
                        "font": font_resource_name if font_resource_name else "默认字体"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"字幕片段添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加字幕片段时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def add_text_animation_effect(
        text_segment_id: str,
        draft_id: str,
        animation_resource_id: Optional[str] = None,
        animation_resource_name: Optional[str] = None,
        duration: Optional[str] = None,
        bubble_effect_id: Optional[str] = None,
        bubble_resource_id: Optional[str] = None,
        flower_effect_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        为字幕片段添加或更新动画和特效
        
        参数:
        - text_segment_id (str): 字幕片段ID，必需
        - draft_id (str): 草稿ID，必需
        - animation_resource_id (str, 可选): 动画资源ID
        - animation_resource_name (str, 可选): 动画资源名称
        - duration (str, 可选): 动画持续时间（秒）
        - bubble_effect_id (str, 可选): 泡泡特效ID
        - bubble_resource_id (str, 可选): 泡泡特效资源ID
        - flower_effect_id (str, 可选): 花字特效资源ID
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和动画特效信息
        """
        
        request_data = {
            "textSegmentId": text_segment_id,
            "draftId": draft_id
        }
        
        # 添加可选参数
        if animation_resource_id and animation_resource_name:
            request_data["type"] = {
                "resourceId": animation_resource_id,
                "resourceName": animation_resource_name
            }
            
        if duration:
            request_data["duration"] = duration
            
        if bubble_effect_id:
            request_data["bubbleEffectId"] = bubble_effect_id
            
        if bubble_resource_id:
            request_data["bubbleResourceId"] = bubble_resource_id
            
        if flower_effect_id:
            request_data["flowerEffectId"] = flower_effect_id
        
        try:
            response = requests.post(
                f"{config.get_base_url()}/segment/text/add-animation-effect",
                json=request_data,
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "字幕动画特效添加成功",
                    "data": result,
                    "animation_info": {
                        "text_segment_id": text_segment_id,
                        "draft_id": draft_id,
                        "animation_type": animation_resource_name if animation_resource_name else "无动画",
                        "duration": duration,
                        "has_bubble_effect": bool(bubble_effect_id),
                        "has_flower_effect": bool(flower_effect_id)
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"字幕动画特效添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": "添加字幕动画特效时发生错误",
                "error": str(e),
                "request_data": request_data
            }
    
    print("✅ 字幕片段管理工具注册完成: add_text_segment, add_text_animation_effect")
