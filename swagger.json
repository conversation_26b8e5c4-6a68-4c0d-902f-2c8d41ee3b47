{"openapi": "3.1.0", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:81/api/jy", "description": "Generated server url"}], "tags": [{"name": "字幕片段管理", "description": "字幕片段相关接口"}, {"name": "素材工具接口", "description": "素材工具接口"}, {"name": "视频片段管理", "description": "视频片段相关接口"}, {"name": "草稿管理", "description": "草稿管理相关接口"}, {"name": "音频片段管理", "description": "音频片段相关接口"}, {"name": "轨道管理", "description": "轨道相关接口"}], "paths": {"/track/add": {"post": {"tags": ["轨道管理"], "summary": "添加轨道", "description": "添加一个新的轨道到MongoDB", "operationId": "addTrack", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackAddReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add": {"post": {"tags": ["视频片段管理"], "summary": "添加视频片段", "description": "添加一个新的视频片段到MongoDB", "operationId": "addVideoSegment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaSegmentAddReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add-transition": {"post": {"tags": ["视频片段管理"], "summary": "给视频片段添加转场特效", "description": "给指定的视频片段添加转场特效", "operationId": "addTransition", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransitionTypeReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add-mask": {"post": {"tags": ["视频片段管理"], "summary": "添加视频蒙版", "description": "为视频片段添加视频蒙版，同一个片段只能有一个蒙版，新蒙版会覆盖旧蒙版", "operationId": "addMask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoMaskReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add-filters": {"post": {"tags": ["视频片段管理"], "summary": "添加视频滤镜", "description": "为视频片段添加视频滤镜，同一类型的滤镜根据resourceId去重，只保留最新的", "operationId": "addFilter", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoFilterReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add-effects": {"post": {"tags": ["视频片段管理"], "summary": "添加视频特效", "description": "为视频片段添加视频特效，同一类型的特效根据resourceId去重，只保留最新的", "operationId": "addEffect", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoEffectReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add-background-filling": {"post": {"tags": ["视频片段管理"], "summary": "给视频片段添加背景填充特效", "description": "给指定的视频片段添加背景填充特效", "operationId": "addBackgroundFilling", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BackgroundFillingReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/video/add-animation": {"post": {"tags": ["视频片段管理"], "summary": "给视频片段添加动画", "description": "给指定的视频片段添加动画效果", "operationId": "addAnimation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoAnimationReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/text/add": {"post": {"tags": ["字幕片段管理"], "summary": "添加字幕片段", "description": "添加一个新的字幕片段到MongoDB", "operationId": "addTextSegment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TextSegmentAddReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/text/add-animation-effect": {"post": {"tags": ["字幕片段管理"], "summary": "添加/更新字幕动画和特效", "description": "为字幕片段添加或更新动画和特效", "operationId": "addOrUpdateTextAnimationAndEffectToSegment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TextAnimationAndEffectReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/audio/add": {"post": {"tags": ["音频片段管理"], "summary": "添加音频片段", "description": "添加一个新的音频片段到MongoDB", "operationId": "addAudioSegment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaSegmentAddReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/audio/add-keyframe": {"post": {"tags": ["音频片段管理"], "summary": "批量添加音频关键帧", "description": "为音频片段批量添加关键帧信息，同一片段的timeOffset和volume组合不能重复", "operationId": "addAudioKeyframe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudioKeyframeReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/audio/add-fade-effect": {"post": {"tags": ["音频片段管理"], "summary": "添加/更新音频淡入淡出特效", "description": "为音频片段添加或更新淡入淡出特效", "operationId": "addAudioFadeEffect", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudioFadeEffectReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/segment/audio/add-effects": {"post": {"tags": ["音频片段管理"], "summary": "添加音频特效", "description": "为音频片段添加音频特效，同一类型的特效根据resourceId去重，只保留最新的", "operationId": "addAudioEffect", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AudioEffectReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseString"}}}}}}}, "/draft/create": {"post": {"tags": ["草稿管理"], "summary": "创建草稿", "description": "创建一个新的草稿", "operationId": "createDraftScript", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DraftCreateReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseDraftCreateRepDto"}}}}}}}, "/materials/utils/media-info": {"get": {"tags": ["素材工具接口"], "summary": "获取素材信息", "description": "获取素材信息,视频,音频,图片", "operationId": "mediaInfo", "parameters": [{"name": "filePath", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataResponseMediaInfo"}}}}}}}, "/draft/{draftId}/export": {"get": {"tags": ["草稿管理"], "summary": "导出草稿为zip", "description": "导出草稿及其所有资源文件为一个zip压缩包，包含元数据、草稿内容和媒体资源", "operationId": "exportDraftAsZip", "parameters": [{"name": "draftId", "in": "path", "description": "草稿ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DataBuffer"}}}}}}}}}, "components": {"schemas": {"TrackAddReqDto": {"type": "object", "description": "轨道添加请求参数", "properties": {"draftId": {"type": "string", "description": "草稿id"}, "trackType": {"type": "string", "description": "轨道类型"}, "trackName": {"type": "string", "description": "轨道名称. 仅在创建第一个同类型轨道时允许不指定."}, "mute": {"type": "boolean", "description": "轨道是否静音. 默认不静音."}, "relativeIndex": {"type": "integer", "format": "int32", "description": "相对(同类型轨道的)图层位置, 越高越接近前景. 默认为0."}, "absoluteIndex": {"type": "integer", "format": "int32", "description": "绝对图层位置, 越高越接近前景. 此参数将直接覆盖相应片段的render_index属性, 供有经验的用户使用. 此参数不能与relative_index同时使用."}}, "required": ["draftId", "mute", "relativeIndex", "trackType"]}, "DataResponseString": {"type": "object", "description": "统一响应实体", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码，200为成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "string", "description": "响应数据体"}}, "required": ["code"]}, "ClipSettings": {"type": "object", "description": "素材片段的图像调节设置", "properties": {"alpha": {"type": "number", "format": "float", "description": "图像不透明度, 0-1"}, "flipHorizontal": {"type": "boolean", "description": "是否水平翻转"}, "flipVertical": {"type": "boolean", "description": "是否垂直翻转"}, "rotation": {"type": "number", "format": "float", "description": "顺时针旋转的**角度**, 可正可负"}, "scaleX": {"type": "number", "format": "float", "description": "水平缩放比例"}, "scaleY": {"type": "number", "format": "float", "description": "垂直缩放比例"}, "transformX": {"type": "number", "format": "float", "description": "水平位移, 单位为半个画布宽"}, "transformY": {"type": "number", "format": "float", "description": "垂直位移, 单位为半个画布高"}}, "required": ["alpha", "flipHorizontal", "flipVertical", "rotation", "scaleX", "scaleY", "transformX", "transformY"]}, "MediaSegmentAddReqDto": {"type": "object", "description": "音频/视频片段添加请求参数", "properties": {"afterSegmentId": {"type": "string", "description": "素材片段添加到哪个片段之后, 可以是任意类型素材的id"}, "draftId": {"type": "string", "description": "素材所属的 draftId"}, "targetTimerange": {"$ref": "#/components/schemas/Timerange", "description": "片段在轨道上的目标时间范围"}, "sourceTimerange": {"$ref": "#/components/schemas/Timerange", "description": "截取的素材片段的时间范围, 默认从开头根据speed截取与target_timerange等长的一部分"}, "speed": {"type": "number", "format": "double", "description": "播放速度, 默认为1.0. 此项与source_timerange同时指定时, 将覆盖target_timerange中的时长"}, "volume": {"type": "number", "format": "double", "description": "音量, 默认为1.0"}, "resourcePath": {"type": "string", "description": "素材实例或素材路径, 这里对应的是一个网络路径, 最后还需要下载的"}, "trackId": {"type": "string", "description": "轨道id"}, "clipSettings": {"$ref": "#/components/schemas/ClipSettings", "description": "素材片段的配置项, 默认为空"}}, "required": ["draftId", "resourcePath", "speed", "targetTimerange", "volume"]}, "Timerange": {"type": "object", "description": "时间范围", "properties": {"start": {"type": "string", "description": "起始时间，当 afterSegmentId 存在时可为空，系统自动计算"}, "duration": {"type": "string", "description": "持续长度"}}}, "Resource": {"type": "object", "description": "资源信息, id和名字", "properties": {"resourceId": {"type": "string", "description": "资源id"}, "resourceName": {"type": "string", "description": "资源名字"}}, "required": ["resourceId", "resourceName"]}, "TransitionTypeReqDto": {"type": "object", "description": "转场特效请求参数", "properties": {"draftId": {"type": "string", "description": "草稿id"}, "videoSegmentId": {"type": "string", "description": "视频片段id"}, "transitionType": {"$ref": "#/components/schemas/Resource", "description": "转场类型"}, "duration": {"type": "string", "description": "转场持续时间, 单位为微秒. 若传入字符串则会调用`tim()`函数进行解析. 若不指定则使用转场类型定义的默认值."}, "realDuration": {"type": "integer", "format": "int64", "description": "读取的转场持续时间"}}, "required": ["draftId", "realDuration", "transitionType", "videoSegmentId"]}, "VideoMaskReqDto": {"type": "object", "description": "视频蒙版类", "properties": {"draftId": {"type": "string", "description": "素材所属的 draftId"}, "segmentId": {"type": "string", "description": "素材所属的片段Id"}, "maskType": {"$ref": "#/components/schemas/Resource", "description": "蒙版类型"}, "centerX": {"type": "number", "format": "float", "description": "蒙版中心点X坐标(以素材的像素为单位), 默认设置在素材中心"}, "centerY": {"type": "number", "format": "float", "description": "蒙版中心点Y坐标(以素材的像素为单位), 默认设置在素材中心"}, "size": {"type": "number", "format": "float", "description": "蒙版的`主要尺寸`(镜面的可视部分高度/圆形直径/爱心高度等), 以占素材高度的比例表示, 默认为0.5"}, "rotation": {"type": "number", "format": "float", "description": "蒙版顺时针旋转的**角度**, 默认不旋转"}, "feather": {"type": "number", "format": "float", "description": "蒙版的羽化参数, 取值范围0~100, 默认无羽化"}, "invert": {"type": "boolean", "description": "是否反转蒙版, 默认不反转"}, "rectWidth": {"type": "number", "format": "float", "description": "矩形蒙版的宽度, 仅在蒙版类型为矩形时允许设置, 以占素材宽度的比例表示, 默认与`size`相同"}, "roundCorner": {"type": "number", "format": "float", "description": "矩形蒙版的圆角参数, 仅在蒙版类型为矩形时允许设置, 取值范围0~100, 默认为0"}}, "required": ["centerX", "centerY", "draftId", "feather", "invert", "maskType", "rotation", "segmentId", "size"]}, "VideoFilter": {"type": "object", "description": "视频滤镜类", "properties": {"filterType": {"$ref": "#/components/schemas/Resource", "description": "滤镜强度(0-100), 仅当所选滤镜能够调节强度时有效. 默认为100."}, "intensity": {"type": "number", "format": "float", "description": "滤镜强度"}}, "required": ["filterType", "intensity"]}, "VideoFilterReqDto": {"type": "object", "description": "视频滤镜类", "properties": {"draftId": {"type": "string", "description": "素材所属的 draftId"}, "segmentId": {"type": "string", "description": "素材所属的片段Id"}, "filters": {"type": "array", "description": "滤镜参数", "items": {"$ref": "#/components/schemas/VideoFilter"}}}, "required": ["draftId", "filters", "segmentId"]}, "VideoEffect": {"type": "object", "description": "视频特效类", "properties": {"effectType": {"$ref": "#/components/schemas/Resource", "description": "特效类型"}, "params": {"type": "array", "description": "特效参数列表, 参数列表中未提供或为None的项使用默认值.\n                参数取值范围(0~100)与剪映中一致. 某个特效类型有何参数以及具体参数顺序以枚举类成员的annotation为准", "items": {"type": "number", "format": "float"}}}, "required": ["effectType"]}, "VideoEffectReqDto": {"type": "object", "description": "视频特效类", "properties": {"draftId": {"type": "string", "description": "素材所属的 draftId"}, "segmentId": {"type": "string", "description": "素材所属的片段Id"}, "effects": {"type": "array", "description": "视频特效参数", "items": {"$ref": "#/components/schemas/VideoEffect"}}}, "required": ["draftId", "effects", "segmentId"]}, "BackgroundFillingReqDto": {"type": "object", "description": "背景填充请求参数", "properties": {"draftId": {"type": "string", "description": "草稿id"}, "videoSegmentId": {"type": "string", "description": "视频片段id"}, "fillType": {"type": "string", "description": "背景填充类型"}, "blur": {"type": "number", "format": "double", "description": "模糊度, 0~1"}, "color": {"type": "string", "description": "填充颜色"}}, "required": ["blur", "color", "draftId", "fillType", "videoSegmentId"]}, "VideoAnimationReqDto": {"type": "object", "description": "视频特效请求参数", "properties": {"type": {"$ref": "#/components/schemas/Resource", "description": "动画类型"}, "duration": {"type": "string", "description": "动画的时间范围"}, "draftId": {"type": "string", "description": "草稿id"}, "videoSegmentId": {"type": "string", "description": "视频片段id"}}, "required": ["draftId", "type", "videoSegmentId"]}, "TextBackground": {"type": "object", "description": "字体背景设置", "properties": {"style": {"type": "integer", "format": "int32", "description": "背景样式, 1和2分别对应剪映中的两种样式, 默认为1"}, "alpha": {"type": "number", "format": "double", "description": "背景不透明度, 与剪映中一致, 取值范围[0, 1], 默认为1.0"}, "color": {"type": "string", "description": "背景颜色, 格式为'#RRGGBB'"}, "roundRadius": {"type": "number", "format": "double", "description": "背景圆角半径, 与剪映中一致, 取值范围[0, 1], 默认为0.0"}, "height": {"type": "number", "format": "double", "description": "背景高度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14"}, "width": {"type": "number", "format": "double", "description": "背景宽度, 与剪映中一致, 取值范围为[0, 1], 默认为0.14"}, "horizontal": {"type": "number", "format": "double", "writeOnly": true}, "vertical": {"type": "number", "format": "double", "writeOnly": true}, "horizontalOffset": {"type": "number", "format": "double", "description": "背景水平偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5"}, "verticalOffset": {"type": "number", "format": "double", "description": "背景竖直偏移, 与剪映中一致, 取值范围为[0, 1], 默认为0.5"}}, "required": ["alpha", "color", "height", "horizontalOffset", "roundRadius", "style", "verticalOffset", "width"]}, "TextBorder": {"type": "object", "description": "文本描边参数", "properties": {"alpha": {"type": "number", "format": "float", "description": "描边不透明度"}, "color": {"type": "array", "description": "描边颜色", "items": {"type": "number", "format": "float"}}, "mappedWidth": {"type": "number", "format": "float", "writeOnly": true}, "width": {"type": "number", "format": "float"}}, "required": ["alpha", "color", "width"]}, "TextSegmentAddReqDto": {"type": "object", "description": "字幕特效请求参数", "properties": {"afterSegmentId": {"type": "string", "description": "素材片段添加到哪个片段之后, 可以是任意类型素材的id"}, "textSegmentId": {"type": "string", "description": "字幕片段id"}, "draftId": {"type": "string", "description": "草稿id"}, "text": {"type": "string", "description": "文本"}, "font": {"$ref": "#/components/schemas/Resource", "description": "字体"}, "style": {"$ref": "#/components/schemas/TextStyle", "description": "样式"}, "border": {"$ref": "#/components/schemas/TextBorder", "description": "边框"}, "clipSettings": {"$ref": "#/components/schemas/ClipSettings", "description": "裁剪设置"}, "background": {"$ref": "#/components/schemas/TextBackground", "description": "背景"}, "trackId": {"type": "string", "description": "轨道id"}, "targetRanger": {"$ref": "#/components/schemas/Timerange", "description": "目标时间范围"}}, "required": ["draftId", "text"]}, "TextStyle": {"type": "object", "description": "字体样式类", "properties": {"size": {"type": "number", "format": "float", "description": "字体大小"}, "bold": {"type": "boolean", "description": "是否加粗"}, "italic": {"type": "boolean", "description": "是否斜体"}, "underline": {"type": "boolean", "description": "是否加下划线"}, "color": {"type": "array", "description": "字体颜色", "items": {"type": "number", "format": "float"}}, "alpha": {"type": "number", "format": "float", "description": "字体不透明度"}, "align": {"type": "integer", "format": "int32", "description": "对齐方式"}, "vertical": {"type": "boolean", "description": "是否为竖排文本"}, "letterSpacing": {"type": "integer", "format": "int32", "description": "字符间距"}, "lineSpacing": {"type": "integer", "format": "int32", "description": "行间距"}, "autoWrapping": {"type": "boolean", "description": "是否自动换行"}, "maxLineWidth": {"type": "number", "format": "float", "description": "最大行宽"}}, "required": ["align", "alpha", "autoWrapping", "bold", "color", "italic", "letterSpacing", "lineSpacing", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "underline", "vertical"]}, "TextAnimationAndEffectReqDto": {"type": "object", "description": "字幕动画和特效请求参数", "properties": {"textSegmentId": {"type": "string"}, "draftId": {"type": "string"}, "type": {"$ref": "#/components/schemas/Resource", "description": "动画类型 00"}, "duration": {"type": "string", "description": "动画持续时间, 单位为秒, 仅对入场/出场动画有效.  00"}, "bubbleEffectId": {"type": "string", "description": "泡泡特效id  11"}, "bubbleResourceId": {"type": "string", "description": "泡泡特效资源id  11"}, "flowerEffectId": {"type": "string", "description": "花字特效资源id 22"}}, "required": ["draftId", "textSegmentId"]}, "AudioKeyframeReqDto": {"type": "object", "description": "音频关键帧请求参数", "properties": {"draftId": {"type": "string", "description": "草稿id"}, "audioSegmentId": {"type": "string", "description": "音频片段id"}, "keyframes": {"type": "array", "description": "关键帧列表，支持批量添加多个关键帧", "items": {"$ref": "#/components/schemas/KeyframeData"}}}, "required": ["audioSegmentId", "draftId", "keyframes"]}, "KeyframeData": {"type": "object", "description": "单个关键帧数据", "properties": {"timeOffset": {"type": "string", "description": "关键帧的时间偏移量, 1s,2s"}, "volume": {"type": "number", "format": "float", "description": "音量在`time_offset`处的值"}}, "required": ["timeOffset", "volume"]}, "AudioFadeEffect": {"type": "object", "description": "音频淡入淡出特效", "properties": {"inDuration": {"type": "string", "description": "淡入时间"}, "outDuration": {"type": "string", "description": "淡出时间"}}, "required": ["inDuration", "outDuration"]}, "AudioFadeEffectReqDto": {"type": "object", "description": "音频淡入淡出特效请求参数", "properties": {"draftId": {"type": "string", "description": "草稿id"}, "audioSegmentId": {"type": "string", "description": "音频片段id"}, "audioFade": {"$ref": "#/components/schemas/AudioFadeEffect", "description": "音频淡入淡出效果"}}, "required": ["audioFade", "audioSegmentId", "draftId"]}, "AudioEffect": {"type": "object", "properties": {"effectType": {"$ref": "#/components/schemas/Resource", "description": "音效类型, 一类音效只能添加一个, 根据 resourceId 去重."}, "params": {"type": "array", "description": "音效参数", "items": {"type": "number", "format": "float"}}}, "required": ["effectType"]}, "AudioEffectReqDto": {"type": "object", "description": "音频特效请求参数", "properties": {"draftId": {"type": "string", "description": "素材所属的 draftId"}, "segmentId": {"type": "string", "description": "素材片段的id"}, "audioEffects": {"type": "array", "description": "音频素材片段特效", "items": {"$ref": "#/components/schemas/AudioEffect"}}}, "required": ["audioEffects", "draftId", "segmentId"]}, "DraftCreateReqDto": {"type": "object", "description": "创建草稿请求参数", "properties": {"width": {"type": "integer", "format": "int32", "description": "草稿宽度"}, "height": {"type": "integer", "format": "int32", "description": "草稿高度"}, "fps": {"type": "integer", "format": "int32", "description": "帧率"}, "name": {"type": "string", "description": "草稿名称"}, "draftPath": {"type": "string", "description": "草稿路径"}, "apiKey": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>"}}, "required": ["draftPath", "fps", "height", "width"]}, "DataResponseDraftCreateRepDto": {"type": "object", "description": "统一响应实体", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码，200为成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/DraftCreateRepDto", "description": "响应数据体"}}, "required": ["code"]}, "DraftCreateRepDto": {"type": "object", "description": "创建草稿响应实体", "properties": {"draftId": {"type": "string", "description": "草稿id"}, "draftName": {"type": "string", "description": "草稿名称"}, "draftContent": {"type": "object", "additionalProperties": {}, "description": "草稿内容"}}, "required": ["draftId"]}, "DataResponseMediaInfo": {"type": "object", "description": "统一响应实体", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码，200为成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/MediaInfo", "description": "响应数据体"}}, "required": ["code"]}, "MediaInfo": {"type": "object", "description": "媒体文件信息", "properties": {"fileName": {"type": "string", "description": "文件名"}, "absolutePath": {"type": "string", "description": "文件路径"}, "fileSize": {"type": "integer", "format": "int64", "description": "文件大小"}, "mimeType": {"type": "string", "description": "文件MIME类型"}, "type": {"type": "string", "description": "文件格式"}, "width": {"type": "integer", "format": "int32", "description": "宽度"}, "height": {"type": "integer", "format": "int32", "description": "高度"}, "durationMicroseconds": {"type": "integer", "format": "int64", "description": " 时长(微秒)，静态图片为null"}, "durationSeconds": {"type": "string", "description": "时长(秒)，静态图片为null"}}, "required": ["absolutePath", "fileName", "fileSize", "mimeType", "type"]}, "DataBuffer": {}}}}