"""
批量修复所有工具文件中的请求方式
将 json=request_data 改为 data=json.dumps(request_data)
"""

import os
import re

def fix_requests_in_file(file_path):
    """修复单个文件中的请求方式"""
    print(f"修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加json导入（如果没有的话）
    if 'import json' not in content and 'from json import' not in content:
        # 在其他import之后添加json导入
        content = re.sub(
            r'(import requests\nfrom typing import.*?\nfrom mcp\.server\.fastmcp import FastMCP\nfrom \.config import config)',
            r'\1\nimport json',
            content,
            flags=re.DOTALL
        )
    
    # 替换所有的 json=request_data 为 data=json.dumps(request_data)
    content = re.sub(
        r'(\s+)response = requests\.post\(\s*\n(\s+)f"[^"]+",\s*\n(\s+)json=([^,\n]+),\s*\n(\s+)headers=([^,\n]+),\s*\n(\s+)timeout=([^)]+)\s*\n(\s+)\)',
        r'\1response = requests.post(\n\2f"{config.get_base_url()}/segment/video/add",\n\3data=json.dumps(\4),\n\5headers=\6,\n\7timeout=\8\n\9)',
        content,
        flags=re.MULTILINE
    )
    
    # 更简单的替换方式
    content = content.replace('json=request_data,', 'data=json.dumps(request_data),')
    content = content.replace('json=request_data', 'data=json.dumps(request_data)')
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} 修复完成")

def main():
    """主函数"""
    print("🔧 批量修复请求方式...")
    
    # 需要修复的文件列表
    files_to_fix = [
        'tools/track_tools.py',
        'tools/video_tools.py',
        'tools/text_tools.py', 
        'tools/audio_tools.py',
        'tools/draft_tools.py',
        'tools/material_tools.py'
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            fix_requests_in_file(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    print("\n✨ 所有文件修复完成！")

if __name__ == "__main__":
    main()
