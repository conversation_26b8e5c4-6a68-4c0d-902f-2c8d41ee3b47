"""
剪映MCP工具配置文件
统一管理所有配置项
"""

import os
from typing import Optional


class Config:
    """配置类"""

    # API基础URL - 可以通过环境变量覆盖
    BASE_URL: str = os.getenv("JIANYING_API_BASE_URL", "http://43.136.184.237:45124/api/jy")

    # 请求超时时间（秒）
    REQUEST_TIMEOUT: int = int(os.getenv("JIANYING_API_TIMEOUT", "30"))

    # 默认API密钥
    DEFAULT_API_KEY: Optional[str] = '476cfb43ba1e45a6ad3e2a8ad28b726e'

    # 请求重试次数
    MAX_RETRIES: int = int(os.getenv("JIANYING_API_MAX_RETRIES", "3"))

    # 是否启用调试模式
    DEBUG: bool = os.getenv("JIANYING_DEBUG", "false").lower() == "true"

    @classmethod
    def get_base_url(cls) -> str:
        """获取API基础URL"""
        return cls.BASE_URL

    @classmethod
    def set_base_url(cls, url: str) -> None:
        """设置API基础URL"""
        cls.BASE_URL = url

    @classmethod
    def get_headers(cls, api_key: Optional[str] = None) -> dict:
        """获取默认请求头"""
        headers = {"Content-Type": "application/json"}

        # 添加API密钥到请求头
        if api_key:
            headers["apiKey"] = api_key
        elif cls.DEFAULT_API_KEY:
            headers["apiKey"] = cls.DEFAULT_API_KEY

        return headers

    @classmethod
    def print_config(cls) -> None:
        """打印当前配置"""
        print("剪映MCP配置:")
        print(f"   API地址: {cls.BASE_URL}")
        print(f"   超时时间: {cls.REQUEST_TIMEOUT}秒")
        print(f"   重试次数: {cls.MAX_RETRIES}")
        print(f"   调试模式: {'开启' if cls.DEBUG else '关闭'}")
        print(f"   API密钥: {'已配置' if cls.DEFAULT_API_KEY else '未配置'}")


# 创建全局配置实例
config = Config()

# 导出常用的配置项
BASE_URL = config.BASE_URL
REQUEST_TIMEOUT = config.REQUEST_TIMEOUT
DEFAULT_API_KEY = config.DEFAULT_API_KEY
MAX_RETRIES = config.MAX_RETRIES
DEBUG = config.DEBUG
