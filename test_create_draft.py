"""
测试create_draft函数修复
对比原始成功函数和修复后的函数
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

from tools.draft_tools import register_draft_tools
from mcp.server.fastmcp import FastMCP

def test_original_function():
    """测试原始成功的函数"""
    print("🧪 测试原始成功的send_req函数...")
    
    # 从1.py导入成功的函数
    sys.path.append('.')
    from importlib import import_module
    module = import_module('1')
    
    try:
        result = module.send_req()
        print(f"✅ 原始函数成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 原始函数失败: {e}")
        return False

def test_fixed_function():
    """测试修复后的函数"""
    print("\n🔧 测试修复后的create_draft函数...")

    # 直接复制修复后的逻辑进行测试
    import json
    import requests
    from tools.config import config

    width = 1920
    height = 1080
    fps = 30
    draft_path = "test_draft.jy"
    name = "测试草稿"
    api_key = "476cfb43ba1e45a6ad3e2a8ad28b726e"

    # 构建请求数据 - 使用与成功函数相同的格式
    request_data = {
        "width": width if width == 0 else width or 1920,
        "height": height if height == 0 else height or 1080,
        "fps": fps if fps == 0 else fps or 30,
        "draftPath": draft_path
    }

    # 添加可选参数
    if name:
        request_data["name"] = name

    # 构建请求头 - 使用固定的API密钥
    headers = {
        "Content-Type": "application/json",
        "apiKey": api_key or "476cfb43ba1e45a6ad3e2a8ad28b726e"
    }

    try:
        # 使用与成功函数相同的请求方式
        response = requests.post(
            f"{config.get_base_url()}/draft/create",
            data=json.dumps(request_data),  # 使用data + json.dumps而不是json参数
            headers=headers,
            timeout=config.REQUEST_TIMEOUT
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                print(f"✅ 修复后函数成功: {data.get('draftId')}")
                return True
            else:
                print(f"❌ 修复后函数失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 修复后函数异常: {e}")
        return False

def compare_requests():
    """对比两个函数的请求差异"""
    print("\n📊 请求差异对比:")
    print("=" * 50)
    
    print("原始成功函数 (send_req):")
    print("- 请求方式: data=json.dumps(data)")
    print("- 请求头: 固定apiKey")
    print("- 数据处理: 有默认值逻辑")
    print("- URL: 直接硬编码")
    
    print("\n修复后函数 (create_draft):")
    print("- 请求方式: data=json.dumps(request_data) ✅")
    print("- 请求头: 固定apiKey ✅") 
    print("- 数据处理: 添加默认值逻辑 ✅")
    print("- URL: 使用配置文件 ✅")
    print("- 超时设置: 添加timeout ✅")

def main():
    """主测试函数"""
    print("🎬 create_draft函数修复测试")
    print("=" * 60)
    
    # 对比请求差异
    compare_requests()
    
    # 测试原始函数
    original_success = test_original_function()
    
    # 测试修复后的函数
    fixed_success = test_fixed_function()
    
    # 总结
    print("\n📋 测试总结:")
    print("=" * 30)
    print(f"原始函数: {'✅ 成功' if original_success else '❌ 失败'}")
    print(f"修复函数: {'✅ 成功' if fixed_success else '❌ 失败'}")
    
    if original_success and fixed_success:
        print("\n🎉 修复成功！两个函数都能正常工作。")
    elif original_success and not fixed_success:
        print("\n⚠️ 修复未完成，需要进一步调试。")
    else:
        print("\n❌ 测试环境可能有问题。")

if __name__ == "__main__":
    main()
