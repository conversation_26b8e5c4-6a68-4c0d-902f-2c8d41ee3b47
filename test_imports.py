"""
测试所有模块导入是否正常
"""

def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        print("1. 测试config导入...")
        from tools.config import config
        print(f"   ✅ config导入成功: {config.get_base_url()}")
        
        print("2. 测试FastMCP导入...")
        from mcp.server.fastmcp import FastMCP
        mcp = FastMCP("测试")
        print("   ✅ FastMCP导入成功")
        
        print("3. 测试track_tools导入...")
        from tools.track_tools import register_track_tools
        register_track_tools(mcp)
        print("   ✅ track_tools导入成功")
        
        print("4. 测试video_tools导入...")
        from tools.video_tools import register_video_tools
        register_video_tools(mcp)
        print("   ✅ video_tools导入成功")
        
        print("5. 测试text_tools导入...")
        from tools.text_tools import register_text_tools
        register_text_tools(mcp)
        print("   ✅ text_tools导入成功")
        
        print("6. 测试audio_tools导入...")
        from tools.audio_tools import register_audio_tools
        register_audio_tools(mcp)
        print("   ✅ audio_tools导入成功")
        
        print("7. 测试draft_tools导入...")
        from tools.draft_tools import register_draft_tools
        register_draft_tools(mcp)
        print("   ✅ draft_tools导入成功")
        
        print("8. 测试material_tools导入...")
        from tools.material_tools import register_material_tools
        register_material_tools(mcp)
        print("   ✅ material_tools导入成功")
        
        print("\n🎉 所有模块导入成功！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
