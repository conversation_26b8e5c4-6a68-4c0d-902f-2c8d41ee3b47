# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/5 下午6:13
File Name:1.py
"""
import json
from typing import Optional, Any, Dict

import requests


def create_draft(
        width: int,
        height: int,
        fps: int,
        draft_path: str,
        name: Optional[str] = None,
        api_key: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建新的草稿

    参数:
    - width (int): 草稿宽度，必需
    - height (int): 草稿高度，必需
    - fps (int): 帧率，必需
    - draft_path (str): 草稿路径，必需
    - name (str, 可选): 草稿名称
    - api_key (str, 可选): API密钥

    返回:
    - Dict[str, Any]: API响应结果，包含草稿ID、名称和内容
    """

    # 构建请求数据
    request_data = {
        "width": width,
        "height": height,
        "fps": fps,
        "draftPath": draft_path
    }

    # 添加可选参数
    if name:
        request_data["name"] = name
    if api_key:
        request_data["apiKey"] = api_key

    # 构建请求头
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["apiKey"] = '476cfb43ba1e45a6ad3e2a8ad28b726e'

    try:
        response = requests.post(
            f"http://43.136.184.237:45124/api/jy/draft/create",
            json=request_data,
            headers=headers
        )

        if response.status_code == 200:
            result = response.json()

            # 检查响应格式
            if result.get("code") == 200:
                data = result.get("data", {})
                return {
                    "success": True,
                    "message": "草稿创建成功",
                    "draft_id": data.get("draftId"),
                    "draft_name": data.get("draftName"),
                    "draft_content": data.get("draftContent"),
                    "api_response": result,
                    "draft_info": {
                        "width": width,
                        "height": height,
                        "fps": fps,
                        "path": draft_path,
                        "name": name or "未命名草稿"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"草稿创建失败: {result.get('message', '未知错误')}",
                    "error_code": result.get("code"),
                    "api_response": result
                }
        else:
            return {
                "success": False,
                "message": f"草稿创建失败，HTTP状态码: {response.status_code}",
                "error": response.text,
                "status_code": response.status_code
            }

    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "message": "网络请求失败",
            "error": str(e),
            "request_data": request_data
        }
    except Exception as e:
        return {
            "success": False,
            "message": "创建草稿时发生未知错误",
            "error": str(e),
            "request_data": request_data
        }


def send_req():
    url = 'http://43.136.184.237:45124/api/jy/draft/create'
    width = 0
    height = 0
    fps = 0
    name = 'dc'
    draftPath = 'ds'
    data = {
        "width": width if width == 0 else 1920,
        "height": height if height == 0 else 1080,
        "fps": fps if fps == 0 else 30,
        "name": name,
        "draftPath": draftPath,
    }

    headers = {
        "Content-Type": "application/json",  # 明确指定 JSON 格式
        "apiKey": "476cfb43ba1e45a6ad3e2a8ad28b726e"
    }
    # 发送POST请求
    response = requests.post(url, data=json.dumps(data), headers=headers)

    # 检查请求是否成功
    response.raise_for_status()
    # 获取响应内容
    draftId = response.json()['data']['draftId']

    # 打印响应内容

    return draftId


if __name__ == "__main__":
    # 示例用法
    print(create_draft(1920, 1080, 30, "draft.jy", "Draft"))
    print(send_req())

