"""
草稿管理工具
包含草稿相关的所有API工具
"""

import requests
from typing import Dict, Any, Optional
from mcp.server.fastmcp import FastMCP

from .config import config

def register_draft_tools(mcp: FastMCP):
    """注册草稿管理工具到MCP服务器"""
    
    @mcp.tool()
    def create_draft(
        width: int,
        height: int,
        fps: int,
        draft_path: str,
        name: Optional[str] = None,
        api_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建新的草稿
        
        参数:
        - width (int): 草稿宽度，必需
        - height (int): 草稿高度，必需
        - fps (int): 帧率，必需
        - draft_path (str): 草稿路径，必需
        - name (str, 可选): 草稿名称
        - api_key (str, 可选): API密钥
        
        返回:
        - Dict[str, Any]: API响应结果，包含草稿ID、名称和内容
        """
        
        # 构建请求数据 - 使用与成功函数相同的格式
        request_data = {
            "width": width if width == 0 else width or 1920,
            "height": height if height == 0 else height or 1080,
            "fps": fps if fps == 0 else fps or 30,
            "draftPath": draft_path
        }

        # 添加可选参数
        if name:
            request_data["name"] = name

        # 构建请求头 - 使用配置的方式
        headers = config.get_headers(api_key)

        try:
            # 使用与成功函数相同的请求方式
            import json
            response = requests.post(
                f"{config.get_base_url()}/draft/create",
                data=json.dumps(request_data),  # 使用data + json.dumps而不是json参数
                headers=headers,
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查响应格式
                if result.get("code") == 200:
                    data = result.get("data", {})
                    return {
                        "success": True,
                        "message": "草稿创建成功",
                        "draft_id": data.get("draftId"),
                        "draft_name": data.get("draftName"),
                        "draft_content": data.get("draftContent"),
                        "api_response": result,
                        "draft_info": {
                            "width": width,
                            "height": height,
                            "fps": fps,
                            "path": draft_path,
                            "name": name or "未命名草稿"
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": f"草稿创建失败: {result.get('message', '未知错误')}",
                        "error_code": result.get("code"),
                        "api_response": result
                    }
            else:
                return {
                    "success": False,
                    "message": f"草稿创建失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "message": "网络请求失败",
                "error": str(e),
                "request_data": request_data
            }
        except Exception as e:
            return {
                "success": False,
                "message": "创建草稿时发生未知错误",
                "error": str(e),
                "request_data": request_data
            }
    
    @mcp.tool()
    def export_draft(draft_id: str, api_key: Optional[str] = None) -> Dict[str, Any]:
        """
        获取草稿导出下载链接

        参数:
        - draft_id (str): 草稿ID，必需
        - api_key (str, 可选): API密钥

        返回:
        - Dict[str, Any]: 包含下载链接和相关信息
        """

        try:
            # 构建下载URL
            download_url = f"{config.get_base_url()}/draft/{draft_id}/export"

            # 如果有API密钥，添加到URL参数中
            if api_key:
                download_url += f"?apiKey={api_key}"
            elif config.DEFAULT_API_KEY:
                download_url += f"?apiKey={config.DEFAULT_API_KEY}"

            return {
                "success": True,
                "message": "草稿导出链接生成成功",
                "draft_id": draft_id,
                "download_url": download_url,
                "export_info": {
                    "filename": f"draft_{draft_id}.zip",
                    "content_type": "application/zip",
                    "instructions": "请复制下载链接到浏览器中打开，或使用下载工具下载文件"
                },
                "usage": {
                    "browser": f"在浏览器中打开: {download_url}",
                    "curl": f"使用curl下载: curl -o draft_{draft_id}.zip '{download_url}'",
                    "wget": f"使用wget下载: wget -O draft_{draft_id}.zip '{download_url}'"
                }
            }

        except Exception as e:
            return {
                "success": False,
                "message": "生成导出链接时发生错误",
                "error": str(e),
                "draft_id": draft_id
            }
    
    print("草稿管理工具注册完成: create_draft, export_draft")
