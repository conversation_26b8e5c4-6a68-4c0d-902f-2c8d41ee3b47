# API 文档规范

## 基本信息
- **标题**: OpenAPI definition
- **版本**: v0
- **服务器地址**: `http://localhost:81/api/jy`

---

## 标签分类
| 标签名称         | 描述               |
|------------------|--------------------|
| 字幕片段管理 | 字幕片段相关接口 |
| 素材工具接口 | 素材工具接口 |
| 视频片段管理 | 视频片段相关接口 |
| 草稿管理 | 草稿管理相关接口 |
| 音频片段管理 | 音频片段相关接口 |
| 轨道管理 | 轨道相关接口 |

---

## 接口详情


### 🛤 添加轨道

- **端点**: `POST /track/add`
- **描述**: 添加一个新的轨道到MongoDB
- **请求体**:

```json
{
    "draftId": "string",      // 草稿ID
    "trackType": "string",    // 轨道类型
    "trackName": "string",    // 轨道名称（首次创建可省略）
    "mute": true,             // 是否静音（默认false）
    "relativeIndex": 0,       // 同类型轨道图层位置（越高越前景）
    "absoluteIndex": 0         // 绝对图层位置（与relativeIndex互斥）
}
```

- **响应**:

```json
{
    "code": 200,
    "message": "string",
    "data": "string"
}
```



### 🎬 添加视频片段

- **端点**: `POST /segment/video/add`
- **描述**: 添加一个新的视频片段到MongoDB
- **请求体**:

```json
{
    "draftId": "string",            // 草稿ID
    "resourcePath": "string",      // 素材网络路径
    "targetTimerange": {            // 目标时间范围
        "start": "string",            // 开始时间
        "duration": "string"          // 持续时间
    },
    "clipSettings": {               // 图像调节设置
        "alpha": 0.5,                 // 不透明度（0-1）
        "flipHorizontal": true,       // 水平翻转
        "rotation": 45.0              // 旋转角度
        // ...其他参数
    }
    // ...其他必填参数
}
```


### 📁 创建草稿

- **端点**: `POST /draft/create`
- **描述**: 创建一个新的草稿项目
- **Header**: `apiKey: string（必需）`
- **请求体**:

```json
{
    "width": 1920,              // 草稿宽度
    "height": 1080,             // 草稿高度
    "fps": 30,                  // 帧率
    "draftPath": "/path/to/draft" // 存储路径
}
```

- **响应**:

```json
{
    "code": 200,
    "data": {
        "draftId": "draft_123"    // 生成的草稿ID
    }
}
```


---

## 通用数据结构

### ⏱ Timerange（时间范围）


```json
{
    "start": "00:00:00",  // 开始时间
    "duration": "00:00:05" // 持续时间
}
```



### 📦 Resource（资源标识）


```json
{
    "resourceId": "res_123",
    "resourceName": "转场特效1"
}
```


> 注：完整模式定义详见原始OpenAPI文档的`components/schemas`部分。
