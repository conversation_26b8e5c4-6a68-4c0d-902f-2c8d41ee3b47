"""
轨道管理工具
包含轨道相关的所有API工具
"""

import requests
from typing import Dict, Any, Optional
from mcp.server.fastmcp import FastMCP
from .config import config

def register_track_tools(mcp: FastMCP):
    """注册轨道管理工具到MCP服务器"""
    
    @mcp.tool()
    def add_track(
        draft_id: str,
        track_type: str,
        mute: bool = False,
        relative_index: int = 0,
        track_name: Optional[str] = None,
        absolute_index: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        添加轨道到草稿
        
        参数:
        - draft_id (str): 草稿ID，必需
        - track_type (str): 轨道类型，必需
        - mute (bool): 轨道是否静音，默认False
        - relative_index (int): 相对(同类型轨道的)图层位置，默认0
        - track_name (str, 可选): 轨道名称，仅在创建第一个同类型轨道时允许不指定
        - absolute_index (int, 可选): 绝对图层位置，不能与relative_index同时使用
        
        返回:
        - Dict[str, Any]: API响应结果，包含操作状态和轨道信息
        """
        
        # 构建请求数据
        request_data = {
            "draftId": draft_id,
            "trackType": track_type,
            "mute": mute,
            "relativeIndex": relative_index
        }
        
        # 添加可选参数
        if track_name is not None:
            request_data["trackName"] = track_name
            
        if absolute_index is not None:
            request_data["absoluteIndex"] = absolute_index
        
        try:
            # 发送POST请求
            response = requests.post(
                f"{config.get_base_url()}/track/add",
                json=request_data,
                headers=config.get_headers(),
                timeout=config.REQUEST_TIMEOUT
            )
            
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "message": "轨道添加成功",
                    "data": result,
                    "track_info": {
                        "draft_id": draft_id,
                        "track_type": track_type,
                        "track_name": track_name,
                        "mute": mute,
                        "relative_index": relative_index,
                        "absolute_index": absolute_index
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"轨道添加失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "message": "网络请求失败",
                "error": str(e),
                "request_data": request_data
            }
        except Exception as e:
            return {
                "success": False,
                "message": "添加轨道时发生未知错误",
                "error": str(e),
                "request_data": request_data
            }
    
    print(" 轨道管理工具注册完成: add_track")
