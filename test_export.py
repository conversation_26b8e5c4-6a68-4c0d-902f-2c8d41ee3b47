"""
测试修改后的export_draft函数
验证是否正确返回下载链接
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

from tools.config import config

def test_export_draft_url_generation():
    """测试导出链接生成"""
    print("🧪 测试export_draft链接生成...")
    
    # 模拟export_draft函数的逻辑
    draft_id = "test-draft-123"
    api_key = "476cfb43ba1e45a6ad3e2a8ad28b726e"
    
    # 构建下载URL
    download_url = f"{config.get_base_url()}/draft/{draft_id}/export"
    
    # 添加API密钥到URL参数中
    if api_key:
        download_url += f"?apiKey={api_key}"
    
    result = {
        "success": True,
        "message": "草稿导出链接生成成功",
        "draft_id": draft_id,
        "download_url": download_url,
        "export_info": {
            "filename": f"draft_{draft_id}.zip",
            "content_type": "application/zip",
            "instructions": "请复制下载链接到浏览器中打开，或使用下载工具下载文件"
        },
        "usage": {
            "browser": f"在浏览器中打开: {download_url}",
            "curl": f"使用curl下载: curl -o draft_{draft_id}.zip '{download_url}'",
            "wget": f"使用wget下载: wget -O draft_{draft_id}.zip '{download_url}'"
        }
    }
    
    print("✅ 导出链接生成成功!")
    print(f"📋 草稿ID: {result['draft_id']}")
    print(f"🔗 下载链接: {result['download_url']}")
    print(f"📁 文件名: {result['export_info']['filename']}")
    
    print("\n💡 使用方法:")
    print(f"   浏览器: {result['usage']['browser']}")
    print(f"   curl: {result['usage']['curl']}")
    print(f"   wget: {result['usage']['wget']}")
    
    return result

def test_different_scenarios():
    """测试不同场景"""
    print("\n🎯 测试不同场景...")
    
    scenarios = [
        {
            "name": "有API密钥",
            "draft_id": "draft-001", 
            "api_key": "test-api-key-123"
        },
        {
            "name": "无API密钥",
            "draft_id": "draft-002",
            "api_key": None
        },
        {
            "name": "长草稿ID",
            "draft_id": "very-long-draft-id-with-uuid-96E1296C-E772-4BE4-A3B0-6D5C31C87C09",
            "api_key": "another-api-key"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📝 场景: {scenario['name']}")
        
        draft_id = scenario['draft_id']
        api_key = scenario['api_key']
        
        # 构建下载URL
        download_url = f"{config.get_base_url()}/draft/{draft_id}/export"
        
        if api_key:
            download_url += f"?apiKey={api_key}"
        elif config.DEFAULT_API_KEY:
            download_url += f"?apiKey={config.DEFAULT_API_KEY}"
        
        print(f"   草稿ID: {draft_id}")
        print(f"   API密钥: {api_key or '使用默认密钥'}")
        print(f"   下载链接: {download_url}")

def show_advantages():
    """显示新方案的优势"""
    print("\n🎉 新方案优势:")
    print("=" * 50)
    
    advantages = [
        "✅ 不需要下载大文件到服务器内存",
        "✅ 用户可以选择下载时机和方式", 
        "✅ 支持断点续传和下载管理器",
        "✅ 减少服务器带宽和存储压力",
        "✅ 更快的响应时间",
        "✅ 支持多种下载工具",
        "✅ 用户体验更好"
    ]
    
    for advantage in advantages:
        print(f"  {advantage}")
    
    print("\n🔧 使用场景:")
    print("  1. 用户在浏览器中直接点击链接下载")
    print("  2. 开发者使用curl/wget等工具下载")
    print("  3. 集成到其他应用中进行批量下载")
    print("  4. 支持下载进度显示和暂停/恢复")

def main():
    """主函数"""
    print("🎬 export_draft函数修改测试")
    print("=" * 60)
    
    # 显示配置信息
    print(f"📡 API基础URL: {config.get_base_url()}")
    print(f"🔑 默认API密钥: {'已配置' if config.DEFAULT_API_KEY else '未配置'}")
    
    # 测试链接生成
    test_export_draft_url_generation()
    
    # 测试不同场景
    test_different_scenarios()
    
    # 显示优势
    show_advantages()
    
    print("\n✨ 测试完成！export_draft函数已优化为返回下载链接。")

if __name__ == "__main__":
    main()
