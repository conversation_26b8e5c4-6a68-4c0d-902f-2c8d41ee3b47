"""
素材工具接口
包含素材相关的所有API工具
"""

import requests
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP

from .config import config

def register_material_tools(mcp: FastMCP):
    """注册素材工具到MCP服务器"""
    
    @mcp.tool()
    def get_media_info(file_path: str) -> Dict[str, Any]:
        """
        获取素材信息（视频、音频、图片）
        
        参数:
        - file_path (str): 文件路径，必需
        
        返回:
        - Dict[str, Any]: API响应结果，包含媒体文件的详细信息
        """
        
        try:
            # 发送GET请求
            response = requests.get(
                f"{config.get_base_url()}/materials/utils/media-info",
                params={"filePath": file_path},
                headers={"Accept": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查响应格式
                if result.get("code") == 200:
                    data = result.get("data", {})
                    
                    # 解析媒体信息
                    media_info = {
                        "file_name": data.get("fileName"),
                        "absolute_path": data.get("absolutePath"),
                        "file_size": data.get("fileSize"),
                        "file_size_mb": round(data.get("fileSize", 0) / (1024 * 1024), 2) if data.get("fileSize") else 0,
                        "mime_type": data.get("mimeType"),
                        "type": data.get("type"),
                        "width": data.get("width"),
                        "height": data.get("height"),
                        "duration_microseconds": data.get("durationMicroseconds"),
                        "duration_seconds": data.get("durationSeconds"),
                        "resolution": f"{data.get('width', 0)}x{data.get('height', 0)}" if data.get("width") and data.get("height") else "N/A"
                    }
                    
                    # 判断媒体类型
                    media_type = "未知"
                    if data.get("type"):
                        file_type = data.get("type").lower()
                        if file_type in ["mp4", "avi", "mov", "mkv", "wmv", "flv"]:
                            media_type = "视频"
                        elif file_type in ["mp3", "wav", "aac", "flac", "ogg", "m4a"]:
                            media_type = "音频"
                        elif file_type in ["jpg", "jpeg", "png", "gif", "bmp", "webp"]:
                            media_type = "图片"
                    
                    return {
                        "success": True,
                        "message": "媒体信息获取成功",
                        "media_type": media_type,
                        "media_info": media_info,
                        "api_response": result,
                        "summary": {
                            "文件名": media_info["file_name"],
                            "类型": media_type,
                            "大小": f"{media_info['file_size_mb']} MB",
                            "分辨率": media_info["resolution"],
                            "时长": media_info["duration_seconds"] + "秒" if media_info["duration_seconds"] else "N/A"
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": f"获取媒体信息失败: {result.get('message', '未知错误')}",
                        "error_code": result.get("code"),
                        "api_response": result,
                        "file_path": file_path
                    }
            else:
                return {
                    "success": False,
                    "message": f"获取媒体信息失败，HTTP状态码: {response.status_code}",
                    "error": response.text,
                    "status_code": response.status_code,
                    "file_path": file_path
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "message": "网络请求失败",
                "error": str(e),
                "file_path": file_path
            }
        except Exception as e:
            return {
                "success": False,
                "message": "获取媒体信息时发生未知错误",
                "error": str(e),
                "file_path": file_path
            }
    
    print("素材工具注册完成: get_media_info")
